{"name": "energea-br", "version": "0.1.0", "private": true, "engines": {"node": "20.x"}, "dependencies": {"@apollo/client": "^3.9.9", "@auth0/auth0-react": "^2.3.0", "@aws-sdk/client-s3": "^3.53.1", "@aws-sdk/s3-request-presigner": "^3.53.1", "@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@cloudinary/react": "^1.13.0", "@cloudinary/url-gen": "^1.21.0", "@material-ui/core": "^4.12.4", "@material-ui/icons": "^4.11.3", "@material-ui/lab": "^4.0.0-alpha.61", "@react-pdf-viewer/core": "^3.12.0", "@react-pdf-viewer/default-layout": "^3.12.0", "@testing-library/jest-dom": "^4.2.4", "@testing-library/react": "^9.3.2", "@testing-library/user-event": "^7.1.2", "apollo-boost": "^0.4.9", "apollo-link-context": "^1.0.20", "auth0-lock": "^14.0.0", "babel-preset-es2015": "^6.24.1", "babel-preset-react": "^6.24.1", "babel-register": "^6.26.0", "chart.js": "^4.4.2", "chartjs-adapter-moment": "^1.0.1", "classnames": "^2.2.6", "cloudinary-core": "^2.13.0", "cloudinary-react": "^1.7.2", "copy-to-clipboard": "^3.3.3", "cpf-cnpj-validator": "^1.0.3", "eslint-plugin-react": "^7.34.1", "graphql": "^16.8.1", "graphql.macro": "^1.4.2", "i18next": "^19.6.3", "i18next-browser-languagedetector": "^6.0.0", "i18next-http-backend": "^1.0.18", "mapbox-gl": "^3.6.0", "material-ui-flags": "^1.0.8", "moment": "^2.29.4", "numeral": "^2.0.6", "owasp-password-strength-test": "^1.3.0", "pdfjs-dist": "3.4.120", "query-string": "^6.4.0", "react": "^18.2.0", "react-apollo": "^3.1.5", "react-background-slider": "^2.0.0", "react-chartjs-2": "^5.2.0", "react-cookie-consent": "^9.0.0", "react-dom": "^16.13.1", "react-gtm-module": "^2.0.11", "react-https-redirect": "^1.1.0", "react-i18next": "^11.7.0", "react-map-gl": "^7.0.15", "react-number-format": "^4.4.1", "react-router": "^6.22.0", "react-router-dom": "^6.22.0", "react-router-hash-link": "^2.0.0", "react-router-sitemap": "^1.2.0", "react-scripts": "^5.0.1", "react-signature-canvas": "^1.0.6", "react-slick": "^0.30.2", "react-swipeable-views": "^0.13.9", "react-swipeable-views-utils": "^0.13.9", "serve": "^14.2.4", "slick-carousel": "^1.8.1", "text-to-image": "^7.0.1", "validator": "^13.7.0", "xlsx": "^0.15.6"}, "scripts": {"start": "serve -s build", "sitemap": "babel-node sitemap-generator.js", "dev": "PORT=3001 react-scripts --openssl-legacy-provider start", "dev-old": "PORT=3001 react-scripts start", "build": "react-scripts --openssl-legacy-provider build", "test": "react-scripts test", "eject": "react-scripts eject", "count-lines": "git ls-files | xargs wc -l", "predeploy": "yarn sitemap"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"babel-cli": "^6.26.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-prettier": "^8.6.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-security": "^1.5.0", "prettier": "^2.8.1"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}