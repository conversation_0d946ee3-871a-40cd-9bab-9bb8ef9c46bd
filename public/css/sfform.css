/* Sequel <PERSON> fonts are imported via src/index.css */

body .bPageBlock,
body #bodyCell .bResource .secondaryPalette,
body .secondaryPalette.bPageBlock,
body .individualPalette .secondaryPalette.bPageBlock,
body .bodyDiv .genericTable,
body .genericPageBlockTable,
body .bodyDiv .bSubBlock,
body .bComponentBlock .bPageBlock,
body .bMyDashboard .bPageBlock,
body.rlHoverFrame .bPageBlock,
body.subjectSelectionPopup div.choicesBox,
body.lookupTab .secondaryPalette.bPageBlock,
body.popupTab .secondaryPalette.bPageBlock,
body.UserTagStatsPage .secondaryPalette.bPageBlock {
  background-color: #fff !important;
  border-bottom: 0px solid #fff !important;
  border-left: 0px solid #fff !important;
  border-right: 0px solid #fff !important;
  -moz-border-radius: 0px !important;
  -webkit-border-radius: 0px !important;
  border-radius: 0px !important;
}

a {
  color: #1a2b4a !important;
  font-family: "<PERSON><PERSON>", sans-serif;
  font-size: 14px !important;
}

#j_id0\:theForm\:j_id5\:j_id32
  > div
  > table
  > tbody
  > tr:nth-child(3)
  > td
  > label {
  color: #333;
  font-family: "Sequel Sans", sans-serif;
  font-size: 14px;
}

#j_id0\:theForm\:j_id5\:j_id32
  > div
  > table
  > tbody
  > tr:nth-child(3)
  > td
  > label:after {
  content: url("http://energea.com.br/images/upload.webp");
  padding-left: 10px;
  height: 14px;
  width: auto;
}

.btn {
  -webkit-appearance: none;
}

#j_id0\:theForm\:j_id5\:j_id32\:j_id36
  > div
  > table
  > tbody
  > tr:nth-child(10)
  > th {
  padding-right: 5px !important;
}

#j_id0\:theForm\:j_id5\:j_id32\:j_id36
  > div
  > table
  > tbody
  > tr:nth-child(11)
  > th {
  padding-right: 5px !important;
}

.bPageBlock.brandSecondaryBrd.apexDefaultPageBlock.secondaryPalette {
  border-top: 0px solid #ffffff;
}

.bPageBlock .detailList tr td,
.bPageBlock .detailList tr th,
.hoverDetail .bPageBlock .detailList tr td,
.hoverDetail .bPageBlock .detailList tr th {
  border: none !important;
}

.btn.casa,
.btn.empresa {
  width: 25%;
  border-radius: 6px !important;
}

#j_id0\:theForm\:j_id5\:j_id32 > div > table > tbody > tr:nth-child(1) > td {
  text-align: left;
  padding-left: 20px;
}

.apexp .bPageBlock .detailList {
  margin-bottom: 10px;
}

input[type="file"] {
  margin-bottom: 15px;
}

.btn.empresa {
  margin-bottom: 15px;
  margin-top: 15px;
  margin-left: 15px;
}

input[type="text"] {
  padding: 12px;
  border-radius: 6px;
  background: rgba(0, 0, 0, 0.05);
  margin-bottom: 10px;
  width: 97%;
  border: 1px solid #dad7d7 !important;
  font-family: "Sequel Sans", sans-serif !important;
  font-size: 16px !important;
  color: #333 !important;
}

input:focus,
textarea:focus {
  outline: #1b2c4a auto 1px;
}

input.btn {
  padding: 12px !important;
  font-size: 16px;
  display: inline-block;
  background: white !important;
  color: #1b2c4a !important;
}

input.btn.active {
  background: #1b2c4a !important;
  color: white !important;
}

textarea {
  padding: 12px;
  border-radius: 6px;
  background: rgba(0, 0, 0, 0.05);
  width: 97%;
  border: 1px solid #dad7d7 !important;
  font-family: "Sequel Sans", sans-serif !important;
  font-size: 16px !important;
  color: #333 !important;
}

label {
  font-size: 15px;
  display: inline-block;
  font-weight: 300;
  width: max-content;
}

input.btn.save {
  display: inline-block;
  font-weight: bold;
  font-size: 14px;
  line-height: 1.75;
  letter-spacing: 0.1em;
  text-transform: uppercase;
  text-align: center;
  vertical-align: middle;
  color: #fff !important;
  background: #8cc63f !important;
  border: 0px;
  border-radius: 50px;
  width: 180px;
  padding: 10px 7px 8px !important;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  -webkit-transition: color 0.5s, background 0.5s;
  transition: color 0.5s, background 0.5s;
  margin-left: 15px;
  margin-top: 15px;
  cursor: pointer;
  font-family: "Sequel Sans", sans-serif;
}

input.btn.save:hover {
  background-color: rgb(98, 138, 44) !important;
  /* color: #8cc63f !important; */
}

input.btn.save:focus {
  outline: 0px solid #1e59a4 !important;
}

#j_id0\:theForm\:j_id5\:j_id32\:fileName {
  margin-left: 15px;
}

#j_id0\:theForm\:j_id5\:j_id32
  > div
  > table
  > tbody
  > tr:nth-child(3)
  > td
  > label {
  margin-left: 15px;
}

.g-recaptcha {
  margin-left: 15px;
}

body .bPageBlock .pbBody .labelCol {
  text-align: left !important;
  font-size: 14px !important;
  padding-left: 15px !important;
}

@media (max-width: 767px) {
  .bPageBlock .detailList .data2Col {
    padding-left: 0;
  }

  input[type="text"] {
    width: 93%;
  }

  textarea {
    width: 93%;
  }

  .btn.casa,
  .btn.empresa {
    width: auto !important;
  }
}

#j_id0\:theForm\:j_id5\:j_id32\:j_id36
  > div
  > table
  > tbody
  > tr:nth-child(10)
  > th
  > label {
  width: max-content !important;
}

body > center {
  font-size: 24px;
  font-family: "Sequel Sans", sans-serif;
  color: #333;
  margin-top: 600px;
}

body > center:before {
  content: url("http://energea.com.br/css/images/energeabrasil.svg");
  width: 250px;
  height: auto;
  margin-bottom: 40px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}
