{"editor.insertSpaces": false, "jshint.enable": false, "eslint.packageManager": "yarn", "eslint.alwaysShowStatus": true, "editor.formatOnSave": true, "workbench.colorCustomizations": {"activityBar.activeBackground": "#2f7c47", "activityBar.background": "#2f7c47", "activityBar.foreground": "#e7e7e7", "activityBar.inactiveForeground": "#e7e7e799", "activityBarBadge.background": "#422c74", "activityBarBadge.foreground": "#e7e7e7", "commandCenter.border": "#e7e7e799", "sash.hoverBorder": "#2f7c47", "statusBar.background": "#215732", "statusBar.foreground": "#e7e7e7", "statusBarItem.hoverBackground": "#2f7c47", "statusBarItem.remoteBackground": "#215732", "statusBarItem.remoteForeground": "#e7e7e7", "titleBar.activeBackground": "#215732", "titleBar.activeForeground": "#e7e7e7", "titleBar.inactiveBackground": "#21573299", "titleBar.inactiveForeground": "#e7e7e799"}, "cSpell.language": "en,pt,pt_BR", "cSpell.words": ["<PERSON><PERSON><PERSON>", "acessível", "actuals", "África", "Agregar", "<PERSON><PERSON><PERSON>", "aman<PERSON><PERSON>", "ambiental", "associados", "Através", "atual", "barata", "bday", "browserslist", "btns", "CAFDs", "cemig", "chartjs", "CHEATSHEET", "clickjacking", "clientes", "climáticos", "cloudinary", "cnpj", "Colorstr", "Conergy", "consciência", "consome", "consomem", "consumidores", "consumo", "contribuem", "countup", "createdb", "crossorigin", "datasets", "dateonly", "DBUSER", "decarbonization", "<PERSON><PERSON><PERSON>", "deserialization", "devtool", "dropdb", "dwolla", "dyno", "dynos", "ecológica", "economia", "Empoderar", "empregos", "empresas", "energea", "Energea’s", "energética", "energia", "Energia", "enfrentar", "escolha", "escolher", "Estados", "eversign", "fatura", "fazemos", "fname", "fotovoltaica", "geram", "<PERSON><PERSON><PERSON>", "gsap", "herokuapp", "hocs", "impacto", "incentivando", "isso", "keyframes", "kpis", "<PERSON><PERSON>", "lazyload", "LDAP", "limpa", "linkedin", "loaderio", "lsof", "mais", "<PERSON><PERSON>", "marketingautomation", "mathjax", "matriz", "maxatte<PERSON>", "microdeposits", "mostrar", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NGOs", "Nico", "nire", "no", "noinfo", "noopen", "nosniff", "nossas", "nosso", "nossos", "<PERSON><PERSON><PERSON>", "OIDS", "okta", "<PERSON><PERSON>'s", "onde", "openidconnect", "oportunidades", "owasp", "pelo", "pensando", "pessoas", "pgdatabase", "pghost", "PGPASSWORD", "PGPORT", "PGUSER", "pkce", "pkey", "planeta", "plusplus", "pode", "poluente", "<PERSON><PERSON>", "postbuild", "preconnect", "progid", "progressbar", "projetos", "própria", "psql", "queremos", "Quickbooks", "readonly", "Reduzir", "refetched", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Roboto", "<PERSON><PERSON><PERSON>", "scrollable", "scrollmagic", "<PERSON><PERSON>", "sess", "SHARPSPRING", "signin", "signup", "<PERSON><PERSON><PERSON><PERSON>", "simplificada", "somos", "styleguide", "Suncast", "sustent<PERSON>vel", "swipeable", "também", "tecnologia", "tornando", "trab<PERSON><PERSON>", "<PERSON>id", "transição", "unacc", "unconfirmedemail", "Unidos", "unlevered", "unregisteredemail", "untrusted", "updatepasswordsuccess", "Uploader", "Usinas", "valorizam", "Vamos", "<PERSON><PERSON><PERSON>", "você", "WCOH", "webcrypto", "xargs", "xirr"], "editor.codeActionsOnSave": {"source.fixAll.eslint": "never"}, "peacock.color": "#215732"}