{
  me {
    brContact {
      id
      createdAt
      activeBrConsumerUnitCount
      activeBrConsumerUnitAverageConsumption
      totalActiveAvgMonthlyConsumptionKwh
      totalConsumerUnitOverdueInvoiceAmount
      brCommissionPaymentChartData {
        id
        date
        value
      }
      brPowerPlans {
        id
        name
        utilityCompany {
          id
          name
        }
        brTariffClass {
          id
          name
        }
        discountRate
        adminResidualCommissionRate
        # adminUpfrontCommissionRate
        salesPersonResidualCommissionRate
        # salesPersonUpfrontCommissionRate
      }
      brSalesPerson {
        id
        brSalesPartnerStatus {
          id
          name
          description
        }
        adminBrContact {
          id
        }
      }
      fullName
      email
      phone
      brContactsBrCustomers {
        id
        role
        brCustomer {
          id
          cnpj
          cpf
          type
          name
          brConsumerUnits {
            id
            name
            # address
            address1
            address2
            city
            district
            state
            postalCode
            countryCode
            installationCode
            utilityCustomerCode

            # associations
            brConsumerUnitStage {
              id
              name
              color
            }
            brTariffClass {
              id
            }
            utilityCompany {
              id
            }
            latestBrCreditCompensation {
              id
              label
              brInvoice {
                id
                barcode
                amountDue
                amountPaid
                brBillingCycle {
                  id
                  billingMonth
                }
                paymentStatus {
                  id
                  label
                  iconClass
                  iconColor
                }
              }
            }
            mostRecentUnpaidBrInvoice {
              id
              stripeInvoice {
                id
                invoiceDownloadUrl
                invoicePaymentUrl
              }
            }

            # consumption
            amountDue
            pastDue

            avgMonthlyConsumption
            # janConsumption
            # febConsumption
            # marConsumption
            # aprConsumption
            # mayConsumption
            # junConsumption
            # julConsumption
            # augConsumption
            # sepConsumption
            # octConsumption
            # novConsumption
            # decConsumption
          }
        }
      }
    }
  }
}
