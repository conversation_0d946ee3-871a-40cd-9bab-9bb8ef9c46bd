query PartnerConsumerUnits(
  $pagination: Pagination
  $sort: Sort
  $filter: BrConsumerUnitFilter
) {
  brConsumerUnitFeed(pagination: $pagination, sort: $sort, filter: $filter) {
    rows {
      id
      adjustedAvgMonthlyConsumption
      name
      pastDue
      salesPartnerNotes
      brCustomer {
        id
        formattedCnpj
        formattedCpf
        type
        name
      }
      brSalesPerson {
        id
        name
      }
      salesPersonBrContact {
        id
        fullName
      }
      createdAt
      installationCode
      brConsumerUnitStage {
        id
        name
      }
      currentBrTermsOfAdhesion {
        id
        signatureDt
        signatureRequestedDt
        brPowerPlan {
          id
          name
        }
        pendingAwsObjectKey
      }
      brTermsOfAdhesions {
        id
        signatureDt
        signatureRequestedDt
        brPowerPlan {
          id
          name
        }
        pendingAwsObjectKey
      }
    }
    count
  }
}
