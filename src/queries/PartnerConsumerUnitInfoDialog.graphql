query getBrConsumerUnit($brConsumerUnitId: Int!) {
  consumerUnit: getBrConsumerUnit(id: $brConsumerUnitId) {
    id
    brCustomer {
      id
      brContactsBrCustomers {
        id
        brContact {
          id
          email
          fullName
          phone
          whatsAppPhoneLink
        }
      }
    }
    recentBrCreditCompensations {
      id
      label
      brInvoice {
        id
        barcode
        amountDue
        amountPaid
        stripeInvoiceId
        starkBankBoletoId
        brBillingCycle {
          id
          billingMonthLabelPortuguese
        }
        paymentStatus {
          id
          labelPortuguese
          iconClass
          iconColor
        }
      }
    }
  }
}
