{
  me {
    brContact {
      id
      fullName
      brSalesPerson {
        id
        pendingAgreementDownloadUrl
        adminBrContact {
          id
        }
      }
      pendingTermsOfAdhesion {
        id
        pendingSignatureDownloadUrl
        brConsumerUnit {
          id
          name
          brCustomer {
            id
            primaryBrContact {
              id
              fullName
            }
          }
        }
      }
    }
  }
}
