query getBrConsumerUnit($brConsumerUnitId: Int!) {
  consumerUnit: getBrConsumerUnit(id: $brConsumerUnitId) {
    id
    recentBrCreditCompensations {
      id
      label
      brInvoice {
        id
        barcode
        amountDue
        amountPaid
        stripeInvoiceId
        starkBankBoletoId
        brBillingCycle {
          id
          billingMonthLabelPortuguese
        }
        paymentStatus {
          id
          labelPortuguese
          iconClass
          iconColor
        }
      }
    }
  }
}
