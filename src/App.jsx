// cachebuster: 1.0.13
import React from "react";
import { Route, Navigate, Routes } from "react-router-dom";

import Auth0ProviderWithHistory from "./components/Auth0ProviderWithHistory";
import Auth0Callback from "./components/Auth0Callback";
import LoginRoute from "./components/LoginRoute";
// import withTranslator from "./components/hocs/withTranslator";
import Home from "./components/Home";

// import withTranslator from "./components/hocs/withTranslator";

// import HowItWorks from "./components/HowItWorks";
// import SavingsCalculator from "./components/SavingsCalculator";
import Register from "./components/Register-New";
import SalesPartnerRegister from "./components/SalesPartnerRegister";
import Partners from "./components/Partners";
// import LearnMore from './components/LearnMore';
// import WhoWeAre from "./components/WhoWeAre";
// import Clients from "./components/Clients";
// import Projects from "./components/Projects";
// import Benefits from "./components/Benefits";
// import Login from "./components/LoginAuth0Redirect";
import Dashboard from "./components/Dashboard";
// import TempDashboardOverride from "./components/TempDashboardOverride";
import Settings from "./components/Settings";

// import Faq from "./components/Faq";
// import Contact from "./components/Contact";
import withHeader from "./components/hocs/withHeader";
import withRequiredAuth from "./components/hocs/withRequiredAuth";

const HomeWithHeader = withHeader(Home, { footer: true, opaque: true });
// const LoginComponent = withHeader(Login, { fullPage: true, opaque: true });
const DashboardWithHeader = withHeader(withRequiredAuth(Dashboard), {
  footer: true,
  opaque: true,
});
const SettingsWithHeader = withHeader(withRequiredAuth(Settings), {
  footer: true,
  opaque: true,
});
const RegisterWithHeader = withHeader(Register, { footer: true, opaque: true });
const SalesPartnerRegisterWithHeader = withHeader(SalesPartnerRegister, {
  footer: true,
  opaque: true,
});
const PartnersWithHeader = withHeader(Partners, { footer: true, opaque: true });

const App = () => {
  return (
    <Auth0ProviderWithHistory>
      <Routes>
        <Route exact path="/" element={<HomeWithHeader />} />
        <Route path="/auth/callback" element={<Auth0Callback />} />
        <Route exact path="/login" element={<LoginRoute />} />
        <Route
          exact
          path="/dashboard"
          element={<DashboardWithHeader footer />}
        />
        <Route path="/settings/*" element={<SettingsWithHeader footer />} />
        <Route exact path="/register" element={<RegisterWithHeader />} />
        <Route
          exact
          path="/cadastro-parceiro"
          element={<SalesPartnerRegisterWithHeader />}
        />
        <Route exact path="/parceiros" element={<PartnersWithHeader />} />

        {/* <Route exact path="/home" component={Home} />
      <Route exact path="/how-it-works" component={HowItWorks} />
      <Route exact path="/savings-calculator" component={SavingsCalculator} />
      <Route exact path="/benefits" component={Benefits} />
      <Route exact path="/who-we-are" component={WhoWeAre} />
      <Route exact path="/clients" component={Clients} />
      <Route exact path="/projects" component={Projects} />
      <Route exact path="/faq" component={Faq} />
      <Route exact path="/contact" component={Contact} /> */}
        <Route exact path="/*" element={<Navigate to="/" />} />
      </Routes>
    </Auth0ProviderWithHistory>
  );
};

export default App;
