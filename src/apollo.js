import { ApolloClient, createHttpLink, InMemoryCache } from "@apollo/client";
import { setContext } from "@apollo/client/link/context";

const httpLink = createHttpLink({
  uri: `${process.env.REACT_APP_API_PATH}/graphql`,
  fetchOptions: {
    timeout: 30000, // 30 second timeout
  },
});

let currentToken = localStorage.getItem("id_token") || null; // Store the access token

// Function to set the token (for use after login)
export const setToken = (token) => {
  currentToken = token;
};

// Function to get Auth0 token from localStorage
const getAuth0Token = () => {
  return currentToken || localStorage.getItem("id_token");
};

const authLink = setContext(async (_, { headers }) => {
  // Get the authentication token from Auth0 (stored in localStorage)
  const token = getAuth0Token();

  // return the headers to the context so httpLink can read them
  return {
    headers: {
      ...headers,
      authorization: token ? `Bearer ${token}` : "",
    },
  };
});

const client = new ApolloClient({
  link: authLink.concat(httpLink),
  cache: new InMemoryCache(),
  name: `energea-br-portal-${process.env.NODE_ENV}`, // NOTE: don't change this
  version: "1.1",
  connectToDevTools: process.env.NODE_ENV === "development",
  dataIdFromObject: (o) => o.id, // every object runs through this and determines the id as the id attribute
});

export default client;
