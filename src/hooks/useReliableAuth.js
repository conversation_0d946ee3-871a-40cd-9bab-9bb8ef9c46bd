import { useState, useEffect } from "react";
import { useAuth0 } from "@auth0/auth0-react";

/**
 * Custom hook that provides more reliable authentication state
 * by combining Auth0 hook state with localStorage token validation
 */
export const useReliableAuth = () => {
  const {
    isAuthenticated: auth0IsAuthenticated,
    isLoading,
    user,
    loginWithRedirect,
    logout,
  } = useAuth0();
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  useEffect(() => {
    const checkAuthState = () => {
      // Check Auth0 state first
      if (auth0IsAuthenticated) {
        setIsAuthenticated(true);
        return;
      }

      // If Auth0 says not authenticated, check localStorage for valid token
      const existingToken = localStorage.getItem("id_token");
      const tokenExpiry = localStorage.getItem("id_token_expires_at");

      if (existingToken && tokenExpiry) {
        const isTokenValid = Date.now() < parseInt(tokenExpiry) * 1000;
        if (isTokenValid) {
          setIsAuthenticated(true);
          return;
        } else {
          // Clean up expired tokens
          localStorage.removeItem("id_token");
          localStorage.removeItem("id_token_expires_at");
        }
      }

      // No valid authentication found
      setIsAuthenticated(false);
    };

    if (!isLoading) {
      checkAuthState();
    }
  }, [auth0IsAuthenticated, isLoading]);

  // Enhanced logout function that clears localStorage and redirects to home
  const enhancedLogout = (options = {}) => {
    // Clear our localStorage tokens
    localStorage.removeItem("id_token");
    localStorage.removeItem("id_token_expires_at");

    // Clear session storage flags
    sessionStorage.removeItem("auth0_login_redirect");
    sessionStorage.removeItem("auth0_login_time");
    sessionStorage.removeItem("dashboard_visited");

    // Call Auth0 logout with proper redirect
    logout({
      logoutParams: {
        returnTo: `${window.location.origin}`,
        ...options.logoutParams,
      },
      ...options,
    });
  };

  return {
    isAuthenticated,
    isLoading,
    user,
    loginWithRedirect,
    logout: enhancedLogout,
  };
};

export default useReliableAuth;
