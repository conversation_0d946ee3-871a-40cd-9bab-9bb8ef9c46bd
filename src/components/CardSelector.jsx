import React from "react";
import classNames from "classnames";

import {
  Card,
  CardActionArea,
  CardContent,
  Grid,
  Typography,
} from "@material-ui/core";
import { withStyles } from "@material-ui/core/styles";

import withFullScreen from "./hocs/withFullScreen";

const styles = (theme) => ({
  typeIcon: {
    fontSize: "1.1rem",
    color: "#666",
  },
  selectedType: {
    color: theme.palette.white.main,
    background: theme.palette.secondary.main,
  },
  unselected: {
    background: theme.palette.grey.main,
  },
  typeText: {
    fontWeight: "normal !important",
    lineHeight: "1.1",
  },
  fontAwesomeNormalize: {
    fontSize: ".9rem",
    padding: "4px 0",
  },
});

const CardSelector = ({
  primaryText,
  primaryTextLine2,
  secondaryText,
  secondaryTextSmall,
  fullScreen,
  classes,
  onClick,
  selected,
  icon,
}) => (
  <Card
    // elevation={selected ? 0 : 8}
    elevation={0}
    className={selected ? classes.selectedType : classes.unselected}
    raised={!selected}
    style={{
      margin: ".5rem",
      textAlign: "center",
      borderRadius: "30px",
    }}
  >
    <CardActionArea onClick={onClick}>
      <CardContent>
        <Grid
          container
          justifyContent="space-between"
          alignItems="flex-start"
          direction="column"
          style={{
            width: fullScreen ? "8rem" : "12rem",
            height: fullScreen ? "8rem" : "12rem",
            textAlign: "left",
            padding: fullScreen ? 0 : ".5rem",
          }}
        >
          <Grid item>
            {icon && (
              <div
                style={{
                  width: fullScreen ? "36px" : "52px",
                  height: fullScreen ? "36px" : "52px",
                  border: "1px solid",
                  borderColor: selected
                    ? "currentColor"
                    : "rgba(14, 15, 12, .1)",
                  borderRadius: "50%",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                {React.cloneElement(icon, {
                  className: classNames(
                    icon.props.className,
                    classes.typeIcon,
                    icon.type === "i" && classes.fontAwesomeNormalize
                  ),
                  style: {
                    ...icon.props.style,
                    color: "inherit",
                    margin: 0,
                  },
                })}
              </div>
            )}
          </Grid>
          <Grid item style={{ minWidth: "5.5rem" }}>
            <Grid container spacing={1}>
              <Grid item style={{ position: "relative" }}>
                <Typography
                  variant="h6"
                  color="inherit"
                  className={classes.typeText}
                >
                  {primaryText}
                </Typography>
                {primaryTextLine2 && (
                  <Typography
                    variant="h6"
                    color="inherit"
                    className={classes.typeText}
                  >
                    {primaryTextLine2}
                  </Typography>
                )}
                {secondaryText && (
                  <Typography
                    variant={secondaryTextSmall ? "body2" : "h6"}
                    color="inherit"
                    className={classes.typeText}
                    style={secondaryTextSmall ? { lineHeight: "1.5" } : null}
                  >
                    {secondaryText}
                  </Typography>
                )}
                {selected && (
                  <Typography
                    variant="h6"
                    color="inherit"
                    style={{ position: "absolute", right: 0 }}
                    className={classes.typeText}
                  >
                    <i
                      className="fa-solid fa-circle-check"
                      style={{
                        right: "-26px",
                        bottom: secondaryText ? 2 : 0,
                        position: "absolute",
                      }}
                    />
                  </Typography>
                )}
              </Grid>
              {/* {selected && (
                <Grid item>
                  <Grid
                    container
                    style={{ height: '100%' }}
                    alignItems="flex-end"
                  >
                    <Typography
                      variant="h6"
                      color="inherit"
                      className={classes.typeText}
                    >
                      <i
                        className="fa-solid fa-circle-check"
                        style={{ marginBottom: '2px' }}
                      />
                    </Typography>
                  </Grid>
                </Grid>
              )} */}
            </Grid>
          </Grid>
        </Grid>
      </CardContent>
    </CardActionArea>
  </Card>
);
export default withFullScreen(
  withStyles(styles, { withTheme: true })(CardSelector)
);
