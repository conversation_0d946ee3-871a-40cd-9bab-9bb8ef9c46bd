import React, { Component } from "react";
import { graphql } from "react-apollo";
import { loader } from "graphql.macro";
import {
  <PERSON><PERSON>,
  Dialog,
  DialogContent,
  DialogTitle,
  IconButton,
  Grid,
  TextField,
  Typography,
  withMobileDialog,
  CircularProgress,
} from "@material-ui/core";
import { Close } from "@material-ui/icons";
import { withStyles } from "@material-ui/core/styles";

import validator from "validator";

import withSnackbar from "./hocs/withSnackbar";
import withTranslator from "./hocs/withTranslator";
import moment from "moment";

const createBrContactMutation = loader(
  "../mutations/CreateBrContactBySalesPartner.graphql"
);

const styles = (theme) => ({});

class ContactCreateDialog extends Component {
  constructor(props) {
    super(props);
    this.state = {};
    this.update = this.update.bind(this);
    this.save = this.save.bind(this);
  }

  update(attr) {
    const that = this;
    return (event) => {
      that.setState({ [attr]: event.target.value });
    };
  }

  validate(attr, val) {
    switch (attr) {
      case "firstName":
      case "lastName":
        return val && val.length >= 2;
      case "email":
        return validator.isEmail(val);
      case "phone":
        return validator.isMobilePhone(val, "pt-BR");
      default:
        return true;
    }
  }

  save() {
    const { createBrContact, snackbar, brCustomerId, onClose } = this.props;
    const { firstName, lastName, email, phone } = this.state;
    this.setState({ loading: true });
    createBrContact({
      variables: {
        input: { email, firstName, lastName, phone, brCustomerId },
      },
    }).then(
      () => {
        snackbar.setState({
          snackbarMessage: `Sucesso`,
          snackbarOpen: true,
          snackbarVariant: "success",
        });
        this.setState({
          firstName: null,
          lastName: null,
          email: null,
          phone: null,
          loading: false,
        });
        onClose();
      },
      (res) => {
        this.setState({ loading: false });
        snackbar.setState({
          snackbarMessage: `Erro ao criar o contato. Por favor, tente novamente mais tarde.`,
          snackbarOpen: true,
          snackbarVariant: "error",
        });
      }
    );
  }

  render() {
    const { fullScreen, open, onClose, i18n, brCustomerId } = this.props;
    const { firstName, lastName, email, phone, loading } = this.state;
    return (
      <Dialog fullScreen={fullScreen} open={!!open} onClose={onClose}>
        <DialogTitle style={{ textAlign: "right" }}>
          <Grid container justifyContent="space-between" alignItems="center">
            <Grid item>
              <Typography color="primary" variant="h2">
                Novo Contato
              </Typography>
            </Grid>
            <Grid item>
              <IconButton aria-label="close" onClick={onClose}>
                <Close />
              </IconButton>
            </Grid>
          </Grid>
        </DialogTitle>
        <DialogContent>
          <Grid container justifyContent="center" spacing={3}>
            <Grid item md={6} xs={12}>
              <TextField
                required
                style={{ background: "#fff" }}
                variant="outlined"
                label="Nome"
                value={firstName || ""}
                onChange={this.update("firstName")}
                fullWidth
                error={firstName && !this.validate("firstName", firstName)}
              />
            </Grid>
            <Grid item md={6} xs={12}>
              <TextField
                required
                style={{ background: "#fff" }}
                variant="outlined"
                label="Último nome"
                value={lastName || ""}
                onChange={this.update("lastName")}
                fullWidth
                error={lastName && !this.validate("lastName", lastName)}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                required
                style={{ background: "#fff" }}
                variant="outlined"
                label="E-mail"
                value={email || ""}
                onChange={this.update("email")}
                fullWidth
                error={email && !this.validate("email", email)}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                style={{ background: "#fff" }}
                variant="outlined"
                label="Digite o número do telefone"
                value={phone || ""}
                onChange={this.update("phone")}
                fullWidth
                error={phone && !this.validate("phone", phone)}
              />
            </Grid>
            <Grid item xs={12}>
              <Button
                style={{
                  marginBottom: "1rem",
                  fontWeight: "bold",
                  width: "100%",
                  borderRadius: "50px",
                }}
                onClick={this.save}
                color="secondary"
                disabled={
                  !brCustomerId ||
                  !firstName ||
                  !lastName ||
                  !email ||
                  !this.validate("firstName", firstName) ||
                  !this.validate("lastName", lastName) ||
                  (phone && !this.validate("phone", phone)) ||
                  !this.validate("email", email) ||
                  loading
                }
                variant="contained"
              >
                {loading ? (
                  <CircularProgress
                    style={{ position: "absolute", color: "white" }}
                  />
                ) : null}
                Salvar
              </Button>
            </Grid>
          </Grid>
        </DialogContent>
      </Dialog>
    );
  }
}

export default withSnackbar(
  graphql(createBrContactMutation, { name: "createBrContact" })(
    withStyles(styles, { withTheme: true })(
      withMobileDialog()(withTranslator(ContactCreateDialog))
    )
  )
);
