import { Video, Image } from 'cloudinary-react';
import {
  Button,
  CircularProgress,
  Fade,
  Grid,
  Hidden,
  Icon,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Typography,
  useMediaQuery,
  withMobileDialog,
} from '@material-ui/core';
export default function VideoComponent(props) {
  const { fullScreen } = props;
  const horizontal = useMediaQuery('(min-aspect-ratio: 16/9)');
  const vertical = useMediaQuery('(max-aspect-ratio: 16/9)');

  return (
    <Fragment>
      <Hidden xlUp>
        <Video
          style={{
            width: horizontal || (!horizontal && !vertical) ? '100vw' : 'auto',
            height: vertical ? '100vh' : 'auto',
            minHeight: '100vh',
            minWidth: '100%',
            overflow: 'hidden',
            position: 'absolute',
            top: '-100%',
            bottom: '-100%',
            left: '-100%',
            right: '-100%',
            margin: 'auto',
          }}
          muted
          playsInline
          // cloudName={constants.cloud_name}
          publicId="energea/global-videos/k12of0fegevzysj8tcvj"
          autoPlay
          // poster={cl
          //   .url('energea/global-videos/k12of0fegevzysj8tcvj')
          //   .replace('.mp4', '.jpg')}
          loop
          sourceTypes={['mp4']}
          width="1920"
          crop="scale"
        />
      </Hidden>{' '}
      <Hidden lgDown>
        <Video
          style={{
            width: horizontal || (!horizontal && !vertical) ? '100vw' : 'auto',
            height: vertical ? '100vh' : 'auto',
            minHeight: '100vh',
            minWidth: '100%',
            overflow: 'hidden',
            position: 'absolute',
            top: '-100%',
            bottom: '-100%',
            left: '-100%',
            right: '-100%',
            margin: 'auto',
          }}
          // poster={cl
          //   .url('energea/global-videos/k12of0fegevzysj8tcvj')
          //   .replace('.mp4', '.jpg')}
          muted
          playsInline
          // cloudName={constants.cloud_name}
          publicId="energea/global-videos/k12of0fegevzysj8tcvj"
          autoPlay
          loop
          sourceTypes={['mp4']}
          // width="1920"
          // crop="scale"
          // autoPlayMode="always"
        />{' '}
      </Hidden>{' '}
    </Fragment>
  );
}
