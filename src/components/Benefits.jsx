import React, { Component, Fragment } from "react";
import { graphql } from "react-apollo";
import { loader } from "graphql.macro";

import {
  <PERSON><PERSON>,
  Divider,
  Grid,
  Grow,
  MobileStepper,
  Typography,
  withMobileDialog,
} from "@material-ui/core";
// import SwipeableViews from 'react-swipeable-views';
// import { autoPlay } from 'react-swipeable-views-utils';
import { KeyboardArrowRight, KeyboardArrowLeft } from "@material-ui/icons";

import { withStyles } from "@material-ui/core/styles";
import withTranslator from "./hocs/withTranslator";
import theme from "../theme";
// import classes from '*.module.css';

const benefitsQuery = loader("../queries/Benefits.graphql");

const styles = (theme) => ({
  iconSize: { fontSize: "2rem" },
});

// const AutoPlaySwipeableViews = autoPlay(SwipeableViews);

class Benefits extends Component {
  constructor(props) {
    super(props);
    this.state = {
      activeStep: 0,
      contactFormOpen: false, // eslint-disable-line
    };
    this.handleNext = this.handleNext.bind(this);
    this.handleBack = this.handleBack.bind(this);
  }

  componentDidMount() {
    this.startTimeout();
  }

  startTimeout() {
    setInterval(() => {
      this.setState({ activeStep: this.state.activeStep + 1 });
    }, 5750);
  }
  getIcons() {
    const { theme } = this.props;
    const classes = [
      "fas fa-bolt",
      "fas fa-money-bill-wave",
      "fas fa-globe-americas",
      "fas fa-hard-hat",
      "fas fa-seedling",
      "fas fa-piggy-bank",
      "fas fa-solar-panel",
      "fas fa-mobile-alt",
    ];
    return classes.map((classText) => (
      <i
        style={{
          marginRight: "1.5rem",
          fontSize: "2.5rem",
          color: theme.palette.secondary.main,
        }}
        className={classText}
      />
    ));
  }

  handleNext() {
    const { activeStep } = this.state;
    this.setState({ activeStep: (activeStep || 0) + 1 });
  }
  handleBack() {
    const { activeStep } = this.state;
    this.setState({ activeStep: (activeStep || 1) - 1 });
  }

  render() {
    const { t, data, i18n } = this.props;
    const { activeStep } = this.state;
    const benefits = data && !data.loading && data.allBrBenefits;
    const maxBenefit =
      benefits &&
      benefits.length > 0 &&
      benefits.reduce((a, b) => {
        const entryA =
          i18n.language === "en" ? a.entryEN.length : a.entryPT.length;
        const entryB =
          i18n.language === "en" ? b.entryEN.length : b.entryPT.length;
        return entryA > entryB ? a : b;
      });
    return (
      <Fragment>
        <Typography
          style={{ marginTop: "2rem", marginBottom: "3rem" }}
          gutterBottom
          variant="h3"
          color="primary"
        >
          {t("linkNames:benefits")}
        </Typography>
        <Grid
          item
          xs={12}
          style={{ marginBottom: "3rem", position: "relative" }}
        >
          <Divider
            style={{ background: "rgba(0,0,0,.2)", marginBottom: "2rem" }}
          />
          {/* <AutoPlaySwipeableViews
            style={{ padding: '0' }}
            slideStyle={{ padding: '0' }}
            // axis={theme.direction === 'rtl' ? 'x-reverse' : 'x'}
            index={activeStep}
            interval={6000}
            onChangeIndex={(step) => this.setState({ activeStep: step })}
            enableMouseEvents
          > */}
          {benefits &&
            benefits.map((benefit, index) => (
              <Grow
                key={`benefit-grid-${benefit.id}`}
                timeout={1000}
                direction="left"
                in={activeStep % benefits.length === index}
              >
                <Grid
                  style={{ position: "absolute", width: "100%" }}
                  container
                  alignItems="center"
                  justifyContent="center"
                >
                  <Grid item>
                    <i
                      style={{
                        marginRight: "1.5rem",
                        fontSize: "2.5rem",
                        color: theme.palette.secondary.main,
                      }}
                      className={benefit.iconClass}
                    />
                  </Grid>
                  <Grid item>
                    <Typography
                      style={{ marginTop: "2px" }}
                      color="primary"
                      variant="h6"
                    >
                      <b>
                        {i18n.language === "en"
                          ? benefit.entryEN
                          : benefit.entryPT}
                      </b>
                    </Typography>
                  </Grid>
                </Grid>
              </Grow>
            ))}
          <Grid
            style={{ visibility: "hidden", width: "100%" }}
            container
            alignItems="center"
            justifyContent="center"
          >
            <Grid item>
              <i
                style={{
                  marginRight: "1.5rem",
                  fontSize: "2.5rem",
                  color: theme.palette.secondary.main,
                }}
                className={maxBenefit.iconClass}
              />
            </Grid>
            <Grid item>
              <Typography
                style={{ marginTop: "2px" }}
                color="primary"
                variant="h6"
              >
                <b>
                  {i18n.language === "en"
                    ? maxBenefit.entryEN
                    : maxBenefit.entryPT}
                </b>
              </Typography>
            </Grid>
          </Grid>
          <Divider
            style={{
              background: "rgba(0,0,0,.2)",
              marginTop: "2rem",
              marginBottom: "1rem",
            }}
          />
          <MobileStepper
            variant="dots"
            steps={benefits.length}
            position="static"
            style={{ background: "rgba(255,255,255,0)" }}
            activeStep={activeStep % benefits.length}
            nextButton={
              <Button size="small" onClick={this.handleNext}>
                {t("benefits:next")}
                {theme.direction === "rtl" ? (
                  <KeyboardArrowLeft />
                ) : (
                  <KeyboardArrowRight />
                )}
              </Button>
            }
            backButton={
              <Button size="small" onClick={this.handleBack}>
                {theme.direction === "rtl" ? (
                  <KeyboardArrowRight />
                ) : (
                  <KeyboardArrowLeft />
                )}
                {t("benefits:back")}
              </Button>
            }
          />
        </Grid>
      </Fragment>
    );
  }
}

export default graphql(benefitsQuery)(
  withStyles(styles, { withTheme: true })(
    withMobileDialog()(withTranslator(Benefits))
  )
);
