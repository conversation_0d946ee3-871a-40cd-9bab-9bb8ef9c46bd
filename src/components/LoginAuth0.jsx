import React, { useEffect, useRef } from "react";
import { Navigate } from "react-router-dom";
import Auth0Lock from "auth0-lock";
import { useAuth0 } from "@auth0/auth0-react";
import { withStyles } from "@material-ui/core/styles";
import { Grid } from "@material-ui/core";

import withSnackbar from "./hocs/withSnackbar";
import withTranslator from "./hocs/withTranslator";

const cl = new cloudinary.Cloudinary({
  cloud_name: process.env.REACT_APP_CLOUDINARY_CLOUD_NAME,
  secure: true,
});

const styles = (theme) => ({
  form: {
    width: "100%",
    marginTop: theme.spacing(1),
  },
  submit: {
    marginTop: theme.spacing(3),
  },
});

const Login = (props) => {
  const { isAuthenticated, isLoading, loginWithRedirect } = useAuth0();
  const theme = useTheme();
  const { classes } = props;
  const { i18n } = props;

  const handleLogin = () => {
    // Set flag and timestamp to indicate user initiated login
    sessionStorage.setItem("auth0_login_redirect", "true");
    sessionStorage.setItem("auth0_login_time", Date.now().toString());
    loginWithRedirect({
      authorizationParams: {
        screen_hint: "login",
        ui_locales: "pt-BR",
      },
      appState: {
        returnTo: "/dashboard?welcome=1",
      },
    });
  };

  if (isLoading) return null;
  if (isAuthenticated) return <Navigate to="/dashboard" />;

  return (
    <>
      <Grid
        container
        justifyContent="center"
        alignItems="center"
        style={{ background: "#fff", height: "100vh" }}
      >
        <Grid
          xs={12}
          item
          style={{ padding: 16, maxWidth: 500, textAlign: "center" }}
        >
          <img
            style={{
              width: "4rem",
              height: "4rem",
              marginTop: "80px",
              marginBottom: "1rem",
            }}
            src={cl.url("energea/energea-global-favicon", {
              width: "80",
              quality: "auto",
              format: "WebP",
            })}
            alt="Energea logo"
          />
          <Typography
            variant="h4"
            style={{
              color: theme.palette.primary.main,
              fontWeight: "bold",
              marginBottom: "1rem",
            }}
          >
            {i18n.t("login", "Login")}
          </Typography>
          <Typography
            variant="body1"
            style={{
              color: theme.palette.text.secondary,
              marginBottom: "2rem",
            }}
          >
            {i18n.t("loginSubtitle", "Access your Energea Brasil dashboard")}
          </Typography>

          <Button
            variant="contained"
            color="primary"
            size="large"
            onClick={handleLogin}
            style={{
              marginTop: "1rem",
              padding: "12px 48px",
              fontSize: "1.1rem",
            }}
          >
            {i18n.t("loginButton", "Login")}
          </Button>
        </Grid>
      </Grid>
    </>
  );
};

export default withSnackbar(
  withStyles(styles, { withTheme: true })(withTranslator(Login))
);
