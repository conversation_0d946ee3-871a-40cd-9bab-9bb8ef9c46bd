import React, { Component, Fragment } from "react";
import { graphql } from "react-apollo";
import { loader } from "graphql.macro";

import {
  Button,
  Grid,
  TextField,
  Typography,
  withMobileDialog,
} from "@material-ui/core";
import { LocationOn } from "@material-ui/icons";
import { withStyles } from "@material-ui/core/styles";

import validator from "validator";

import withTranslator from "./hocs/withTranslator";
import withSnackbar from "./hocs/withSnackbar";

const sendContactUsEmailMutation = loader(
  "../mutations/SendContactUsEmail.graphql"
);

const styles = (theme) => ({});

class Contact extends Component {
  constructor(props) {
    super(props);
    this.state = {
      contactFormOpen: false, // eslint-disable-line
    };
    this.update = this.update.bind(this);
    this.sendEmail = this.sendEmail.bind(this);
  }

  update(attr) {
    const that = this;
    return (event) => {
      that.setState({ [attr]: event.target.value });
    };
  }

  sendEmail() {
    const { mutate, snackbar } = this.props;
    const { firstName, lastName, email, message } = this.state;
    mutate({
      variables: { email, firstName, lastName, message },
    })
      .catch((res) => {
        const errors = res.graphQLErrors.map((error) => error.message);
        console.log(errors);
        this.setState({ loading: false });
        snackbar.setState({
          snackbarMessage: `Sorry, error sending contact email! Please try again later.`,
          snackbarOpen: true,
          snackbarVariant: "error",
        });
      })
      .then(() => {
        snackbar.setState({
          snackbarMessage: `Email sent. We'll be in touch shortly!`,
          snackbarOpen: true,
          snackbarVariant: "success",
        });
        this.setState({
          firstName: null,
          lastName: null,
          email: null,
          message: null,
          loading: false,
        });
      });
  }

  render() {
    const { t } = this.props;
    const { firstName, lastName, message, email } = this.state;
    return (
      <Fragment>
        <Grid container justifyContent="center">
          <Typography
            gutterBottom
            color="primary"
            style={{ margin: "2rem 0 3rem", width: "100%" }}
            variant="h3"
          >
            {t("linkNames:contact")}
          </Typography>
          <Grid item md={4} xs={12}>
            <Grid container style={{ marginBottom: "1rem" }}>
              <Grid item>
                <LocationOn
                  style={{
                    marginRight: ".5rem",
                    marginTop: "2px",
                    fontSize: "16px",
                  }}
                  color="secondary"
                />
              </Grid>
              <Grid item>
                <Typography variant="body2">
                  Rua Barão de Jaguaripe, 280/501
                </Typography>
                <Typography variant="body2">Impanema, RJ</Typography>
                <Typography variant="body2">22.421-000</Typography>
              </Grid>
            </Grid>
            {/* <Grid container style={{ marginBottom: '1rem' }}>
              <Grid item>
                <Phone
                  style={{
                    marginRight: '.5rem',
                    marginTop: '2px',
                    fontSize: '16px',
                  }}
                  color="secondary"
                />
              </Grid>
              <Grid item>
                <Typography variant="body2">55 21 3042-3674</Typography>
              </Grid>
            </Grid> */}
          </Grid>
          <Grid
            // component={Paper}
            item
            md={8}
            xs={12}
          >
            <Grid container justifyContent="center" spacing={2}>
              <Grid item xs={6}>
                <TextField
                  required
                  style={{ background: "#fff" }}
                  variant="outlined"
                  label={t("contact:firstName")}
                  value={firstName || ""}
                  onChange={this.update("firstName")}
                  fullWidth
                />
              </Grid>
              <Grid item xs={6}>
                <TextField
                  required
                  style={{ background: "#fff" }}
                  variant="outlined"
                  label={t("contact:lastName")}
                  value={lastName || ""}
                  onChange={this.update("lastName")}
                  fullWidth
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  required
                  style={{ background: "#fff" }}
                  variant="outlined"
                  label={t("contact:email")}
                  value={email || ""}
                  onChange={this.update("email")}
                  fullWidth
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  required
                  multiline
                  style={{ background: "#fff" }}
                  variant="outlined"
                  label={t("contact:message")}
                  rows="4"
                  value={message || ""}
                  onChange={this.update("message")}
                  fullWidth
                />
              </Grid>
              <Grid>
                <Button
                  style={{ marginBottom: "1rem", fontWeight: "bold" }}
                  onClick={this.sendEmail}
                  color="primary"
                  disabled={
                    !firstName ||
                    !lastName ||
                    !message ||
                    firstName === "" ||
                    lastName === "" ||
                    message === "" ||
                    !validator.isEmail(email)
                  }
                  variant="contained"
                >
                  {t("contact:send")}
                </Button>
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      </Fragment>
    );
  }
}

export default withSnackbar(
  graphql(sendContactUsEmailMutation)(
    withStyles(styles, { withTheme: true })(withMobileDialog()(Contact))
  )
);
