import React, { Component, Fragment } from "react";

import {
  Grid,
  Hidden,
  Icon,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Typography,
  useMediaQuery,
  withMobileDialog,
} from "@material-ui/core";
import { Close } from "@material-ui/icons";

import { withStyles } from "@material-ui/core/styles";
import { Image } from "cloudinary-react";
import cloudinary from "cloudinary-core";

const cl = new cloudinary.Cloudinary({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  secure: true,
});
const styles = (theme) => ({});

class LearnMore extends Component {
  constructor(props) {
    super(props);
    this.state = {
      contactFormOpen: false, // eslint-disable-line
    };
  }

  render() {
    const { t } = this.props;
    return (
      <Fragment>
        <Grid
          container
          style={{ backgroundColor: "#fff" }}
          justifyContent="center"
        >
          <Typography color="primary" variant="h3">
            {t("linkNames:learnMore")}
          </Typography>
        </Grid>
      </Fragment>
    );
  }
}

export default withStyles(styles, { withTheme: true })(
  withMobileDialog()(LearnMore)
);
