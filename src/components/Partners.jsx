import React from "react";
import { useNavigate } from "react-router-dom";
import {
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  Grid,
  Typography,
  useTheme,
  useMediaQuery,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Box,
} from "@material-ui/core";
import { Expand<PERSON><PERSON>, ArrowForward } from "@material-ui/icons";
import { withStyles } from "@material-ui/core/styles";

const styles = (theme) => ({
  heroSection: {
    backgroundImage: `url('https://res.cloudinary.com/energea/image/upload/c_scale,q_auto,w_2400/v1/energea/energy-infrastructure/Super-Wide-Blue-Sky.WebP')`,
    backgroundSize: "cover",
    backgroundPosition: "center",
    color: "white",
    minHeight: "70vh",
    display: "flex",
    alignItems: "center",
    position: "relative",
    overflow: "hidden",
    "&::before": {
      content: '""',
      position: "absolute",
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      background:
        "linear-gradient(90deg, #1B2C4A 0%, rgba(27, 44, 74, 0.8) 30%, rgba(27, 44, 74, 0.4) 60%, rgba(27, 44, 74, 0) 100%)",
      zIndex: 1,
    },
  },
  heroContent: {
    position: "relative",
    zIndex: 2,
  },
  sectionTitle: {
    marginBottom: theme.spacing(6),
    fontWeight: 700,
  },
  faqSection: {
    backgroundColor: "#f8f9fa",
    padding: theme.spacing(10, 0),
  },
  companySection: {
    backgroundColor: "white",
    padding: theme.spacing(10, 0),
  },
  accordion: {
    marginBottom: theme.spacing(2),
    // borderRadius: "12px !important",
    borderRadius: theme.shape.borderRadius,
    "&:before": {
      display: "none",
    },
    backgroundColor: theme.palette.grey.main,
  },
  accordionSummary: {
    // backgroundColor: theme.palette.primary.main,
    // color: "white",
    // borderRadius: "12px 12px 0 0",
    // "&.Mui-expanded": {
    //   borderRadius: "12px 12px 0 0",
    // },
    padding: ".5rem 1.5rem",
  },
  accordionDetails: {
    backgroundColor: "white",
    padding: "1.5rem",

    // borderRadius: "0 0 12px 12px",
  },
  benefitCard: {
    height: "100%",
    // borderRadius: "16px",
    transition: "transform 0.3s ease-in-out",
    "&:hover": {
      transform: "translateY(-4px)",
    },
    background: theme.palette.grey.main,
  },
  ctaButton: {
    borderRadius: "50px",
    padding: "1rem 2rem",
    fontSize: "1.2rem",
  },
});

const Partners = ({ classes }) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

  const faqData = [
    {
      question: "Preciso ter experiência no setor de energia?",
      answer:
        "Não. O programa foi pensado para receber pessoas de diferentes áreas. Fornecemos treinamento e materiais de apoio para que você tenha segurança na prospecção e no relacionamento com clientes.",
    },
    {
      question: "Como funciona a remuneração do Parceiro?",
      answer:
        "O parceiro recebe uma comissão de até 5% sobre o valor pago pelo cliente à Energea. É um modelo de receita recorrente: enquanto o cliente permanecer ativo, você continua recebendo mensalmente.",
    },
    {
      question: "Posso atuar em meio período?",
      answer:
        "Sim. Muitos parceiros começam dedicando algumas horas por semana e ampliam sua atuação conforme a carteira de clientes cresce.",
    },
    {
      question:
        "Onde a Energea já possui usinas ativas prontas para receber clientes?",
      answer:
        "Atualmente temos usinas em operação no Rio de Janeiro (Light), em Minas Gerais (CEMIG), em Nova Friburgo – RJ (Energisa) e em Goiás (Equatorial), todas prontas para receber novos clientes.",
    },
    {
      question: "A parceria tem restrições regionais?",
      answer:
        "Nosso objetivo é expandir em várias regiões, mas priorizamos áreas estratégicas e limitamos o número de parceiros por cidade para garantir qualidade no atendimento.",
    },
    {
      question: "Qual é o investimento necessário para começar?",
      answer:
        "Não há necessidade de investimento em estrutura ou estoque. O Parceiro precisa apenas de dedicação, proatividade e conexão com pessoas e empresas. O suporte comercial fica por conta da Energea.",
    },
    {
      question: "Existe contrato ou vínculo empregatício?",
      answer:
        "Não. O Parceiro atua de forma independente, com flexibilidade de horários, mas com o respaldo de uma marca sólida no mercado de energia limpa.",
    },
    {
      question: "Como funciona o processo de cadastro?",
      answer:
        "O parceiro realiza o cadastro pelo portal, escolhe o tipo de parceria e envia os documentos necessários. Após análise, recebe a confirmação do status.",
    },
    {
      question: "Quais são os status possíveis no processo de aprovação?",
      answer: (
        <>
          <ul>
            <li>Em análise – Documentos em revisão pela equipe Energea.</li>
            <li>
              Credenciado – Cadastro aprovado, contrato assinado e parceria
              ativa.
            </li>
            <li>Reprovado – Documentos não validados.</li>
            <li>Cancelado – Parceria encerrada.</li>
          </ul>
        </>
      ),
    },
    {
      question: "Em quanto tempo começo a receber?",
      answer:
        "A sua comissão começa a ser contabilizada assim que o cliente indicado concluir a adesão e tiver a primeira fatura ativa. Vale lembrar que o prazo para que o cliente comece a receber a injeção dos nossos créditos de energia é de até 90 dias.",
    },
    {
      question: "Preciso vender apenas para conhecidos?",
      answer:
        "Não. Fornecemos ferramentas e estratégias de prospecção para que você alcance novos clientes de forma profissional, expandindo sua rede além dos contatos pessoais.",
    },
    {
      question: "Como recebo minhas comissões?",
      answer:
        "O pagamento é feito mensalmente, diretamente em sua conta bancária, de forma transparente e com relatórios acessíveis no nosso sistema.",
    },
    {
      question: "Quais são os diferenciais da Energea para meus clientes?",
      answer:
        "Além da economia imediata na conta de luz, nossos clientes têm a confiança de uma empresa especializada em energia limpa e a tranquilidade de um atendimento próximo e personalizado.",
    },
  ];

  const companyValues = [
    {
      title: "Uma empresa em crescimento",
      icon: "fas fa-chart-line",
      description:
        "Na Energea, estamos em constante expansão, mas sem abrir mão da experiência e do conhecimento que já conquistamos no setor de energia. Unimos inovação, solidez e responsabilidade para oferecer aos nossos parceiros uma base segura de crescimento.",
    },
    {
      title: "Energia limpa que transforma",
      icon: "fas fa-leaf",
      description:
        "Não fornecemos apenas energia limpa: criamos impacto positivo. Nosso trabalho contribui para a transição energética do Brasil, reduzindo custos para nossos clientes e levando desenvolvimento sustentável a diferentes regiões do país.",
    },
    {
      title: "Crescemos juntos",
      icon: "fas fa-handshake",
      description:
        "Acreditamos que o sucesso só faz sentido quando é compartilhado. Por isso, trabalhamos lado a lado com nossos parceiros para que todos cresçam conosco. Nossa rede é feita de pessoas que acreditam no poder da colaboração e do futuro da energia no Brasil.",
    },
    {
      title: "Oportunidades para a sociedade brasileira",
      icon: "fas fa-users",
      description:
        "Na Energea, acreditamos que nosso papel vai além da energia limpa. Com nosso programa de treinamento, damos a chance para que cada vez mais pessoas conheçam o mercado de energia e encontrem novas formas de gerar renda. Assim, ajudamos a movimentar a economia, impulsionar histórias de transformação e expandir o uso consciente e sustentável da energia em todo o Brasil.",
    },
  ];

  return (
    <div>
      {/* Hero Section */}
      <div className={classes.heroSection}>
        <Grid container justifyContent="center">
          <Grid item xs={12} md={10} lg={8} className={classes.heroContent}>
            <Box px={isMobile ? 2 : 4} py={8}>
              <Typography
                variant={isMobile ? "h3" : "h2"}
                component="h1"
                gutterBottom
                style={{ fontWeight: 700, marginBottom: theme.spacing(3) }}
              >
                Programa de Parceiros Energea
              </Typography>
              <Typography
                variant={isMobile ? "h6" : "h5"}
                component="h2"
                style={{
                  marginBottom: theme.spacing(4),
                  opacity: 0.9,
                  fontWeight: 400,
                }}
              >
                Junte-se a nós e faça parte da revolução da energia limpa
              </Typography>
              <Button
                size="large"
                color="secondary"
                variant="contained"
                onClick={() => navigate("/cadastro-parceiro")}
                endIcon={<ArrowForward />}
                style={{
                  borderRadius: "50px",
                  padding: "1rem 2rem",
                  fontSize: "1.2rem",
                }}
              >
                Começar Agora
              </Button>
            </Box>
          </Grid>
        </Grid>
      </div>

      {/* Company Values Section */}
      <div className={classes.companySection}>
        <Grid container justifyContent="center">
          <Grid item xs={12} md={10} lg={8}>
            <Box px={isMobile ? 2 : 4}>
              <Typography
                variant="h2"
                component="h2"
                className={classes.sectionTitle}
                color="primary"
                align="center"
              >
                Por que escolher a Energea?
              </Typography>

              <Grid container spacing={4}>
                {companyValues.map((value, index) => (
                  <Grid item xs={12} md={6} key={index}>
                    <Card className={classes.benefitCard} elevation={0}>
                      <CardContent style={{ padding: theme.spacing(3) }}>
                        <Box display="flex" alignItems="center" mb={2}>
                          <Box
                            display="flex"
                            alignItems="center"
                            justifyContent="center"
                            style={{
                              width: 48,
                              height: 48,
                              minWidth: 48,
                              flexShrink: 0,
                              borderRadius: "50%",
                              backgroundColor: theme.palette.secondary.main,
                              marginRight: theme.spacing(2),
                            }}
                          >
                            <i
                              className={value.icon}
                              style={{
                                fontSize: "16px",
                                color: "white",
                              }}
                            />
                          </Box>
                          <Typography
                            variant="h5"
                            component="h3"
                            style={{
                              fontWeight: 600,
                              color: theme.palette.primary.main,
                            }}
                          >
                            {value.title}
                          </Typography>
                        </Box>
                        <Typography
                          variant="body1"
                          style={{
                            color: theme.palette.blue3.main,
                            lineHeight: 1.6,
                          }}
                        >
                          {value.description}
                        </Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </Box>
          </Grid>
        </Grid>
      </div>

      {/* FAQ Section */}
      <div className={classes.faqSection}>
        <Grid container justifyContent="center">
          <Grid item xs={12} md={10} lg={8}>
            <Box px={isMobile ? 2 : 4}>
              <Typography
                variant="h2"
                component="h2"
                className={classes.sectionTitle}
                color="primary"
                align="center"
              >
                Perguntas Frequentes
              </Typography>

              {faqData.map((faq, index) => (
                <Accordion
                  key={index}
                  className={classes.accordion}
                  elevation={0}
                >
                  <AccordionSummary
                    expandIcon={<ExpandMore />}
                    className={classes.accordionSummary}
                  >
                    <Typography style={{ fontWeight: 600 }}>
                      {faq.question}
                    </Typography>
                  </AccordionSummary>
                  <AccordionDetails className={classes.accordionDetails}>
                    <Typography
                      variant="body1"
                      style={{ color: theme.palette.blue3.main }}
                    >
                      {faq.answer}
                    </Typography>
                  </AccordionDetails>
                </Accordion>
              ))}
            </Box>
          </Grid>
        </Grid>
      </div>
      {/* Final CTA */}
      <div className={classes.companySection}>
        <Grid container justifyContent="center">
          <Grid item xs={12} md={10} lg={8}>
            <Box px={isMobile ? 2 : 4} textAlign="center">
              <Typography
                variant="h4"
                component="h3"
                style={{
                  marginBottom: theme.spacing(3),
                  fontWeight: 600,
                  color: theme.palette.primary.main,
                }}
              >
                Pronto para começar?
              </Typography>
              <Typography
                variant="h6"
                style={{
                  marginBottom: theme.spacing(4),
                  color: theme.palette.blue3.main,
                }}
              >
                Cadastre-se agora e faça parte da nossa rede de parceiros
              </Typography>
              <Button
                size="large"
                color="secondary"
                variant="contained"
                onClick={() => navigate("/cadastro-parceiro")}
                endIcon={<ArrowForward />}
                style={{
                  borderRadius: "50px",
                  padding: "1rem 2rem",
                  fontSize: "1.2rem",
                }}
              >
                Cadastrar-se como Parceiro
              </Button>
            </Box>
          </Grid>
        </Grid>
      </div>
    </div>
  );
};

export default withStyles(styles)(Partners);
