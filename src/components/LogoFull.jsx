import React from "react";
import { withStyles } from "@material-ui/core/styles";

import { ReactComponent as LogoSvg } from "../assets/logo-full.svg";

const styles = (theme) => ({
  // Logo color switching styles
  "logo-dark": {
    "& .logo-text": {
      fill: `${theme.palette.white.main} !important`,
    },
  },
  "logo-light": {
    "& .logo-text": {
      fill: `${theme.palette.primary.main} !important`,
    },
  },
  "logo-leaf": {
    "& .logo-leaf": {
      fill: `${theme.palette.secondary.main} !important`,
    },
  },
});

const Logo = ({ classes, darkMode = false, width = "100px", ...props }) => {
  const logoClasses = [
    classes["logo-leaf"], // Always apply leaf styling
    darkMode ? classes["logo-dark"] : classes["logo-light"], // Apply text color based on mode
  ]
    .filter(Boolean)
    .join(" ");

  return (
    <LogoSvg
      className={logoClasses}
      style={{
        verticalAlign: "middle",
        width,
        height: "auto",
      }}
      alt="Energea Logo"
      title="Energea - Renewable Energy Investing for All"
      {...props}
    />
  );
};

export default withStyles(styles)(Logo);
