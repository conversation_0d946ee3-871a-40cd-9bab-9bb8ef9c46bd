// cachebuster: 1
import React, { Component, useState } from "react";
import classNames from "classnames";
import validator from "validator";
import moment from "moment";
import { Util } from "cloudinary-core";
import {
  <PERSON><PERSON>,
  Card,
  CardActionArea,
  CardContent,
  CircularProgress,
  Collapse,
  IconButton,
  InputAdornment,
  FormControl,
  FormHelperText,
  Grid,
  Hidden,
  Icon,
  MenuItem,
  Select,
  Step,
  StepLabel,
  Stepper,
  TextField,
  Typography,
  withMobileDialog,
} from "@material-ui/core";
import {
  Add,
  VisibilityOffOutlined,
  VisibilityOutlined,
} from "@material-ui/icons";
import { withStyles } from "@material-ui/core/styles";
import { graphql } from "react-apollo";
import { loader } from "graphql.macro";
import NumberFormat from "react-number-format";
import { cpf, cnpj } from "cpf-cnpj-validator";
import owasp from "owasp-password-strength-test";

import withSnackbar from "./hocs/withSnackbar";
import { setToken } from "../apollo";
import withAuth from "./hocs/withAuth";
import withTranslator from "./hocs/withTranslator";
import i18n from "../i18n";
import CardSelector from "./CardSelector";

owasp.config({
  allowPassphrases: false, // When true, passwords > minPhraseLength get to bypass special character requirements in the owasp check. This is not inline with how auth0 works
  maxLength: 128,
  minLength: 8,
  minPhraseLength: 20, // Not applicable when allowPassphrases: false
  minOptionalTestsToPass: 4,
});

const owaspTranslations = {
  "The password must be at least 8 characters long.":
    "A senha deve ter pelo menos 8 caracteres de comprimento.",
  "The password must contain at least one lowercase letter.":
    "A senha deve conter pelo menos uma letra minúscula.",
  "The password must contain at least one uppercase letter.":
    "A senha deve conter pelo menos uma letra maiúscula.",
  "The password must contain at least one special character.":
    "A senha deve conter pelo menos um caractere especial.",
};

const logToSlackMutation = loader("../mutations/LogToSlack.graphql");
const selfRegisterSalesPartnerMutation = loader(
  "../mutations/SelfRegisterSalesPartner.graphql"
);
const getAuth0TokensMutation = loader("../mutations/GetAuth0Tokens.graphql");
const isAuthenticatedQuery = loader("../queries/IsAuthenticated.graphql");

const styles = (theme) => ({});

const PhoneFormat = (props) => {
  const [numberFormat, setNumberFormat] = useState("+55 (##) ####-####");
  const { inputRef, onChange, ...other } = props;

  const mobileNineIndex = 4;
  return (
    <NumberFormat
      {...other}
      getInputRef={inputRef}
      onValueChange={(values) => {
        const { value } = values;
        // NOTE: We use mobileNineIndex - 2 because the value here does not include the leading 55 but it does below
        if (value.length >= 3) {
          if (value[mobileNineIndex - 2] !== "9") {
            setNumberFormat("+55 (##) ####-####");
          } else if (
            value[mobileNineIndex - 2] === "9" &&
            value.length !== 10
          ) {
            setNumberFormat("+55 (##) # ####-####");
          }
        }
        onChange({
          target: { ...values, name: props.name },
        });
      }}
      onBlur={(event) => {
        const phoneValue = event.currentTarget?.value?.replace(/\D+/g, "");
        if (phoneValue.length === 12) {
          setNumberFormat("+55 (##) ####-####");
        }
        if (
          phoneValue.length === 13 &&
          phoneValue[parseInt(mobileNineIndex, 10)] !== "9"
        ) {
          setNumberFormat("+55 (##) ####-####");
        }
      }}
      onFocus={(event) => {
        const phoneValue = event.target?.value?.replace(/\D+/g, "");
        if (
          phoneValue.length <= 12 &&
          phoneValue.length >= mobileNineIndex + 1 &&
          phoneValue[parseInt(mobileNineIndex, 10)] === "9"
        ) {
          setNumberFormat("+55 (##) # ####-####");
        }
      }}
      isNumericString
      allowEmptyFormatting
      format={numberFormat}
      type="tel"
    />
  );
};

const CPFFormat = (props) => {
  const { inputRef, onChange, ...other } = props;
  return (
    <NumberFormat
      {...other}
      getInputRef={inputRef}
      onValueChange={(values) => {
        onChange({
          target: { ...values, name: props.name },
        });
      }}
      isNumericString
      format="###.###.###-##"
    />
  );
};

const CNPJFormat = (props) => {
  const { inputRef, onChange, ...other } = props;
  return (
    <NumberFormat
      {...other}
      getInputRef={inputRef}
      onValueChange={(values) => {
        onChange({
          target: { ...values, name: props.name },
        });
      }}
      isNumericString
      format="##.###.###/####-##"
    />
  );
};

const formLabelColor = "rgba(0, 0, 0, 0.54)";
const inputLabelTextVariant = "body2";

class Register extends Component {
  constructor(props) {
    super(props);
    this.state = {
      activeStep: 0,
      hidePassword: true,

      // inputs:
      type: null,
      cpf: null,
      cnpj: null,
      firstName: null,
      lastName: null,
      telefone: null,
      email: null,
      companyName: null,
      companyLegalName: null,
      jobTitle: null,
      nire: null,
      rgRne: null,
      orgaoExpedidor: null,
      dob: null,
      nationality: null,
      operatingRegions: [],
      bankName: null,
      accountNumber: null,
      agency: null,
      bankTaxNumber: null,
      pix: null,
      referral: null,
      salesExperience: null,
      address1: null,
      address2: null,
      district: null,
      city: null,
      state: null,
      postalCode: null,
      photoIdCloudinaryPublicId: null,
      photoIdFileName: null,
    };
    this.handleClose = this.handleClose.bind(this);
    this.handleNext = this.handleNext.bind(this);
    this.handleBack = this.handleBack.bind(this);
    this.uploadImageWithCloudinary = this.uploadImageWithCloudinary.bind(this);
  }

  async handleSubmit() {
    const {
      cpf,
      cnpj,
      firstName,
      lastName,
      telefone,
      address1,
      address2,
      district,
      city,
      state,
      postalCode,
      salesExperience,
      referral,
      bankName,
      bankTaxNumber,
      pix,
      agency,
      accountNumber,
      email,
      companyName,
      companyLegalName,
      jobTitle,
      password,
      operatingRegions,
      photoIdCloudinaryPublicId,
      photoIdFileName,
    } = this.state;
    const { selfRegisterSalesPartner, snackbar, logToSlack } = this.props;

    this.setState({ loading: true });
    let input = {};
    input = {
      cpf,
      cnpj,
      jobTitle,
      businessName: companyName,
      businessLegalName: companyLegalName,
      contactFirstName: firstName,
      contactLastName: lastName,
      phone: telefone,
      email,
      password,
      salesExperience,
      referral,
      operatingRegions,
      address1,
      address2,
      district,
      city,
      state,
      postalCode,
      commissionBankName: bankName,
      commissionBankAccountNumber: accountNumber,
      commissionBankAgency: agency,
      commissionBankAccountTaxNumber: bankTaxNumber,
      commissionPix: pix,
      photoIdCloudinaryPublicId,
      photoIdFileName,
    };

    // Can't figure out how registering partners have been able to submit with null email/password but that has become somewhat common. Hopefully this helps the UX. It has to be set in state in order for the button to be enabled but it's not here when we post the data. -Joe 9/11/2025
    if (!email || !password) {
      this.setState({ loading: false });
      return;
    }

    selfRegisterSalesPartner({
      variables: { input },
    }).then(
      async () => {
        this.setState({ loading: false });
        snackbar.setState({
          snackbarMessage: `Informações enviadas. Entraremos em contato em breve!`,
          snackbarOpen: true,
          snackbarVariant: "success",
        });

        // Automatically log in the user after successful registration
        try {
          const { getAuth0Tokens, isAuthenticatedRefetch } = this.props;

          // Step 1: Get tokens from backend using email/password
          const tokenData = await getAuth0Tokens({
            variables: {
              input: {
                email,
                password,
              },
            },
          }).then((res) => res.data.getAuth0Tokens);

          // Step 2: Set the token in local storage so that the backend can use it
          if (tokenData?.access_token && tokenData.expires_in) {
            const { access_token, expires_in } = tokenData;

            // Set flag to indicate this is manual authentication FIRST
            sessionStorage.setItem("manual_auth_success", "true");

            localStorage.setItem("id_token", access_token);
            localStorage.setItem(
              "id_token_expires_at",
              Math.floor((Date.now() + expires_in * 1000) / 1000)
            );
            setToken(access_token);

            // Trigger storage event to notify other components of token change
            window.dispatchEvent(
              new StorageEvent("storage", {
                key: "id_token",
                newValue: access_token,
                oldValue: null,
                storageArea: localStorage,
              })
            );

            // Refetch authentication state to update the app
            if (isAuthenticatedRefetch) {
              await isAuthenticatedRefetch();
            }

            // Show success message and redirect
            snackbar.setState({
              snackbarMessage: `Login successful! Redirecting to dashboard...`,
              snackbarOpen: true,
              snackbarVariant: "success",
            });

            // Redirect to dashboard after successful authentication
            // Use window.location.href to force a full page reload with the new token
            setTimeout(() => {
              // Force a full page reload to ensure all components start with correct auth state
              // Build URL with only non-null parameters
              const urlParams = new URLSearchParams();
              const dashboardUrl = `/dashboard${
                urlParams.toString() ? "?" + urlParams.toString() : ""
              }`;
              window.location.href = dashboardUrl;
            }, 2000);
          } else {
            // If token authentication fails, fall back to Auth0 Universal Login
            setTimeout(() => {
              // Build fallback URL with only non-null parameters
              const urlParams = new URLSearchParams();
              const returnToUrl = `/dashboard${
                urlParams.toString() ? "?" + urlParams.toString() : ""
              }`;
              window.location.href = `/login?returnTo=${encodeURIComponent(
                returnToUrl
              )}`;
            }, 2000);
          }
        } catch (error) {
          console.error("Auto-login error:", error);
          // If auto-login fails, fall back to Auth0 Universal Login
          setTimeout(() => {
            // Build fallback URL with only non-null parameters
            const urlParams = new URLSearchParams();
            const returnToUrl = `/dashboard${
              urlParams.toString() ? "?" + urlParams.toString() : ""
            }`;
            window.location.href = `/login?returnTo=${encodeURIComponent(
              returnToUrl
            )}`;
          }, 2000);
        }
      },
      (err) => {
        logToSlack({
          variables: {
            input: {
              title:
                "Error creating new Sales partner from sales partner register form (energea.com.br)",
              type: "credit-management-customers",
              data: [
                {
                  label: "Error Message",
                  value: JSON.stringify(err?.message || err),
                },
                {
                  label: "Input",
                  value: JSON.stringify(input),
                },
              ],
            },
          },
        });
        snackbar.setState({
          snackbarMessage:
            err.message ||
            `Erro ao criar usuário. Por favor, tente novamente mais tarde ou entre em contato conosco <NAME_EMAIL>`,
          snackbarOpen: true,
          snackbarVariant: "error",
        });
        this.setState({ loading: false });
      }
    );
  }

  uploadImageWithCloudinary() {
    const uploadOptions = {
      tags: ["sales-partner-id"],
      showPoweredBy: false,
      multiple: false,
      cloudName: "energea",
      uploadPreset: "njtahvir",
      clientAllowedFormats: ["jpg", "jpeg", "png"],
      sources: ["local", "camera"], // limit sources
      language: "pt-BR",
      text: {
        "pt-BR": {
          drop_files: "Arraste e solte um arquivo aqui", // replaces "Drag & Drop"
          or: "ou",
          back: "Voltar",
          close: "Fechar",
          browse: "Procurar",
          no_results: "Sem resultados",
          search_placeholder: "Pesquisar arquivos",
          menu: {
            files: "Meus arquivos",
            camera: "Câmera",
          },
          queue: {
            title: "Envio de arquivos",
          },
          crop: {
            title: "Cortar",
            skip: "Pular",
            apply: "Aplicar",
          },
          // …add any other keys you need
        },
      },
    };
    const scOptions = Util.withSnakeCaseKeys(uploadOptions);
    window.cloudinary.openUploadWidget(scOptions, (error, resp) => {
      if (!error && resp.event === "success") {
        this.setState({
          photoIdCloudinaryPublicId: resp.info.public_id,
          photoIdFileName: resp.info.original_filename,
        });
        this.handleNext();
      }
    });
  }

  handleClose() {
    this.setState({
      type: null,
      cpf: null,
      cnpj: null,
      firstName: null,
      lastName: null,
      telefone: null,
      address1: null,
      address2: null,
      district: null,
      city: null,
      state: null,
      postalCode: null,
      email: null,
      companyName: null,
      companyLegalName: null,
      jobTitle: null,
      nire: null,
      rgRne: null,
      orgaoExpedidor: null,
      dob: null,
      nationality: null,
      civilStatus: null,
      activeStep: 0,
      operatingRegions: [],
      bankName: null,
      accountNumber: null,
      agency: null,
      bankTaxNumber: null,
      pix: null,
      referral: null,
      salesExperience: null,
    });
  }

  handleBack() {
    const { activeStep } = this.state;
    switch (activeStep) {
      case 0:
        this.setState({ type: null });
        break;
      default:
        this.setState({ activeStep: activeStep - 1 });
        break;
    }
  }

  handleNext() {
    const { activeStep } = this.state;
    switch (activeStep) {
      case 0:
      case 1:
      case 2:
      case 3:
      case 4:
      case 5:
        this.setState({ activeStep: activeStep + 1 });
        break;
      case 6:
        this.handleSubmit();
        break;
      default:
        this.setState({ activeStep: activeStep + 1 });
        break;
    }
  }

  renderStepper() {
    const { activeStep, type } = this.state;
    if (!type) return null;
    switch (activeStep) {
      case 0:
        return this.renderStep0();
      case 1:
        return this.renderStep1();
      case 2:
        return this.renderStep2();
      case 3:
        return this.renderStep3();
      case 4:
        return this.renderStep4();
      case 5:
        return this.renderStep5();
      case 6:
        return this.renderStep6();
      default:
        return null;
    }
  }

  renderActionButtons() {
    const { i18n } = this.props;
    const { loading } = this.state;
    return (
      <Grid
        item
        container
        justifyContent="space-between"
        style={{ marginTop: "1rem" }}
        alignItems="center"
      >
        <Grid item>
          <Button
            variant="text"
            color="primary"
            onClick={() => {
              this.handleBack();
            }}
            disabled={loading}
          >
            {i18n.t("back", "Back")}
          </Button>
        </Grid>
        <Grid item>
          <Button
            variant="contained"
            color="primary"
            onClick={() => {
              this.handleNext();
            }}
            disabled={this.getContinueDisabled() || loading}
          >
            {loading ? (
              <CircularProgress
                style={{ position: "absolute", color: "white" }}
              />
            ) : null}{" "}
            {i18n.t("continue", "Continue")}
          </Button>
        </Grid>
      </Grid>
    );
  }

  renderNIREInput() {
    const { nire } = this.state;
    return (
      <Grid item>
        <Typography
          style={{ color: formLabelColor }}
          gutterBottom
          variant={inputLabelTextVariant}
        >
          NIRE
        </Typography>
        <FormControl fullWidth>
          <TextField
            variant="outlined"
            label="Digite o NIRE"
            fullWidth
            value={nire || ""}
            onChange={(event) =>
              this.setState({
                nire: event.target.value,
              })
            }
          />
        </FormControl>
      </Grid>
    );
  }

  renderCompanyLegalNameInput() {
    const { companyLegalName } = this.state;
    return (
      <Grid item>
        <Typography
          style={{ color: formLabelColor }}
          gutterBottom
          variant={inputLabelTextVariant}
        >
          Razão Social
        </Typography>
        <FormControl required fullWidth>
          <TextField
            variant="outlined"
            required
            label="Razão social da empresa"
            fullWidth
            value={companyLegalName || ""}
            onChange={(event) =>
              this.setState({
                companyLegalName: event.target.value,
              })
            }
            error={
              companyLegalName &&
              !this.validate("companyLegalName", companyLegalName)
            }
          />
        </FormControl>
      </Grid>
    );
  }

  renderCompanyNameInput() {
    const { companyName } = this.state;
    return (
      <Grid item>
        <Typography
          style={{ color: formLabelColor }}
          gutterBottom
          variant={inputLabelTextVariant}
        >
          Nome Fantasia
        </Typography>
        <FormControl required fullWidth>
          <TextField
            variant="outlined"
            required
            label="Nome fantasia da empresa"
            fullWidth
            value={companyName || ""}
            onChange={(event) =>
              this.setState({
                companyName: event.target.value,
              })
            }
            error={companyName && !this.validate("companyName", companyName)}
          />
        </FormControl>
      </Grid>
    );
  }

  renderPlainTextInput({ stateAttr, label, prompt, required }) {
    const value = this.state[stateAttr];
    return (
      <Grid item>
        <Typography
          style={{ color: formLabelColor }}
          gutterBottom
          variant={inputLabelTextVariant}
        >
          {label}
        </Typography>
        <FormControl required={!!required} fullWidth>
          <TextField
            variant="outlined"
            required={!!required}
            label={prompt}
            fullWidth
            value={value || ""}
            onChange={(event) =>
              this.setState({
                [stateAttr]: event.target.value,
              })
            }
            error={value && !this.validate(stateAttr, value)}
          />
        </FormControl>
      </Grid>
    );
  }

  renderCNPJInput() {
    const { cnpj } = this.state;
    return (
      <Grid item>
        <Typography
          style={{ color: formLabelColor }}
          gutterBottom
          variant={inputLabelTextVariant}
        >
          CNPJ
        </Typography>
        <FormControl required fullWidth>
          <TextField
            variant="outlined"
            required
            label="Digite o CNPJ"
            fullWidth
            value={cnpj || ""}
            onChange={(event) =>
              this.setState({
                cnpj: event.target.value,
              })
            }
            InputProps={{
              inputComponent: CNPJFormat,
            }}
            error={cnpj && !this.validate("cnpj", cnpj)}
          />
        </FormControl>
      </Grid>
    );
  }
  renderDistribuidoraInput() {
    const { i18n } = this.props;
    const { distribuidora } = this.state;

    const utilityCompanyOptions = [
      "Light",
      "CEMIG",
      "Energisa Nova Friburgo",
      "Equatorial Goiás",
    ];
    return (
      <Grid item>
        <FormControl required fullWidth error={distribuidora === "Other"}>
          <Typography
            style={{ color: formLabelColor }}
            gutterBottom
            variant={inputLabelTextVariant}
          >
            {i18n.t("distributor", "Distributor")}
          </Typography>
          <Select
            fullWidth
            variant="outlined"
            id="distribuidora"
            name="distribuidora"
            required
            value={distribuidora || "placeholder"}
            onChange={(event) =>
              this.setState({
                distribuidora: event.target.value,
              })
            }
            renderValue={(val) => {
              if (val === "placeholder")
                return (
                  <Typography style={{ color: formLabelColor }}>
                    {i18n.t("chooseDistributor", "Choose a distributor")} *
                  </Typography>
                );
              return <Typography>{val}</Typography>;
            }}
          >
            {utilityCompanyOptions.map((option) => (
              <MenuItem
                key={`distribuidora-selector-${option
                  .toLowerCase()
                  .replace(" ", "-")}`}
                name={`distribuidora-${option.toLowerCase().replace(" ", "-")}`}
                value={option}
              >
                {option}
              </MenuItem>
            ))}
            <MenuItem
              key={`distribuidora-selector-other`}
              name="distribuidora-other"
              value="Other"
            >
              {i18n.t("other", "Other")}
            </MenuItem>
          </Select>
          <FormHelperText>
            {distribuidora === "Other" &&
              i18n.t(
                "weCanOnlyAcceptCustomersFromTheUtilityCompaniesListedAbove",
                "We can only accept customers from the utility companies listed above."
              )}
          </FormHelperText>
        </FormControl>
      </Grid>
    );
  }
  renderOperatingRegionInput() {
    const { i18n } = this.props;
    const { operatingRegions } = this.state;

    // NOTE: These strings are compared on the backend. Changes to these will require a change on the backend
    const operatingRegionOptions = [
      "Rio (Light)",
      "Nova Friburgo (Energisa Nova Friburgo)",
      "Goiás (Equatorial Goiás)",
      "Minas (CEMIG)",
    ];
    return (
      <Grid item>
        <FormControl required fullWidth>
          <Typography
            style={{ color: formLabelColor }}
            gutterBottom
            variant={inputLabelTextVariant}
          >
            Áreas de atuação - Você pode escolher mais de uma.
          </Typography>
          <Select
            fullWidth
            multiple
            variant="outlined"
            id="operatingRegions"
            name="operatingRegions"
            required
            value={operatingRegions || []}
            onChange={(event) => {
              this.setState({
                operatingRegions: event.target.value,
              });
            }}
            renderValue={(val) => {
              if (val === "placeholder")
                return (
                  <Typography style={{ color: formLabelColor }}>
                    Escolha uma área de atuação *
                  </Typography>
                );
              return <Typography>{val.join(", ")}</Typography>;
            }}
          >
            {operatingRegionOptions.map((option) => (
              <MenuItem
                key={`region-selector-${option
                  .toLowerCase()
                  .replace(" ", "-")}`}
                name={`region-${option.toLowerCase().replace(" ", "-")}`}
                value={option}
              >
                {option}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
      </Grid>
    );
  }
  renderRgRneInput() {
    const { rgRne } = this.state;
    return (
      <Grid item>
        <Typography
          style={{ color: formLabelColor }}
          gutterBottom
          variant={inputLabelTextVariant}
        >
          RG/RNE
        </Typography>
        <FormControl required fullWidth>
          <TextField
            variant="outlined"
            required
            label="Digite o RG/RNE"
            fullWidth
            value={rgRne || ""}
            onChange={(event) =>
              this.setState({
                rgRne: event.target.value,
              })
            }
          />
        </FormControl>
      </Grid>
    );
  }

  renderOrgaoExpedidorInput() {
    const { orgaoExpedidor } = this.state;
    return (
      <Grid item>
        <Typography
          style={{ color: formLabelColor }}
          gutterBottom
          variant={inputLabelTextVariant}
        >
          Órgão expedidor
        </Typography>
        <FormControl required fullWidth>
          <TextField
            variant="outlined"
            required
            label="Digite o órgão expedidor do documento"
            fullWidth
            value={orgaoExpedidor || ""}
            onChange={(event) =>
              this.setState({
                orgaoExpedidor: event.target.value,
              })
            }
          />
        </FormControl>
      </Grid>
    );
  }

  renderDOBInput() {
    const { dob } = this.state;
    return (
      <Grid item>
        <Typography
          style={{ color: formLabelColor }}
          gutterBottom
          variant={inputLabelTextVariant}
        >
          Data de nascimento
        </Typography>
        <FormControl required fullWidth>
          <TextField
            variant="outlined"
            required
            label="Digite a data de nascimento"
            fullWidth
            type="date"
            value={dob || ""}
            onChange={(event) =>
              this.setState({
                dob: event.target.value,
              })
            }
            InputLabelProps={{ shrink: true }}
            error={dob && !this.validate("dob", dob)}
          />
        </FormControl>
      </Grid>
    );
  }

  renderNationalityInput() {
    const { nationality } = this.state;
    return (
      <Grid item>
        <Typography
          style={{ color: formLabelColor }}
          gutterBottom
          variant={inputLabelTextVariant}
        >
          Nacionalidade
        </Typography>
        <FormControl required fullWidth>
          <TextField
            variant="outlined"
            required
            label="Digite a nacionalidade"
            fullWidth
            value={nationality || ""}
            onChange={(event) =>
              this.setState({
                nationality: event.target.value,
              })
            }
          />
        </FormControl>
      </Grid>
    );
  }

  renderCivilStatusInput() {
    const { civilStatus } = this.state;
    return (
      <Grid item>
        <Typography
          style={{ color: formLabelColor }}
          gutterBottom
          variant={inputLabelTextVariant}
        >
          Estado civil
        </Typography>
        <FormControl required fullWidth>
          <Select
            fullWidth
            variant="outlined"
            id="selecione-o-estado-civil"
            name="selecione-o-estado-civil"
            required
            // label="Test"
            // labelWidth={50}
            value={civilStatus || "placeholder"}
            onChange={(event) =>
              this.setState({
                civilStatus: event.target.value,
              })
            }
            renderValue={(val) => {
              if (val === "placeholder")
                return (
                  <Typography style={{ color: formLabelColor }}>
                    Selecione o estado civil *
                  </Typography>
                );
              if (val === `casado`) return <Typography>Casado (a)</Typography>;
              if (val === `solteiro`)
                return <Typography>Solteiro (a)</Typography>;
              if (val === `viúvo`) return <Typography>Viúvo (a)</Typography>;
              if (val === `divorciado`)
                return <Typography>Divorciado (a)</Typography>;
              return <Typography>{val}</Typography>;
            }}
          >
            <MenuItem
              key="civil-status-casado"
              name="civil-status-casado"
              value="casado"
            >
              Casado (a)
            </MenuItem>
            <MenuItem
              key="civil-status-solteiro"
              name="civil-status-solteiro"
              value="solteiro"
            >
              Solteiro (a)
            </MenuItem>
            <MenuItem
              key="civil-status-viúvo"
              name="civil-status-viúvo"
              value="viúvo"
            >
              Viúvo (a)
            </MenuItem>
            <MenuItem
              key="civil-status-divorciado"
              name="civil-status-divorciado"
              value="divorciado"
            >
              Divorciado (a)
            </MenuItem>
          </Select>
        </FormControl>
      </Grid>
    );
  }

  renderCPFInput() {
    const { i18n } = this.props;
    const { cpf } = this.state;
    return (
      <Grid item>
        <Typography
          style={{ color: formLabelColor }}
          gutterBottom
          variant={inputLabelTextVariant}
        >
          CPF
        </Typography>
        <FormControl required fullWidth>
          <TextField
            variant="outlined"
            required
            label={i18n.t("enterTheCPF", "Enter the CPF")}
            fullWidth
            value={cpf || ""}
            onChange={(event) =>
              this.setState({
                cpf: event.target.value,
              })
            }
            InputProps={{
              inputComponent: CPFFormat,
            }}
            error={cpf && !this.validate("cpf", cpf)}
          />
        </FormControl>
      </Grid>
    );
  }

  renderFirstNameInput() {
    const { i18n } = this.props;
    const { firstName } = this.state;
    return (
      <Grid item>
        <Typography
          style={{ color: formLabelColor }}
          gutterBottom
          variant={inputLabelTextVariant}
        >
          {i18n.t("firstName", "First Name")}
        </Typography>
        <FormControl required fullWidth>
          <TextField
            variant="outlined"
            required
            label={i18n.t("enterYourFirstName", "Enter your first name")}
            fullWidth
            value={firstName || ""}
            onChange={(event) =>
              this.setState({
                firstName: event.target.value,
              })
            }
            error={firstName && !this.validate("firstName", firstName)}
          />
        </FormControl>
      </Grid>
    );
  }

  renderSalesExperienceInput() {
    const { i18n } = this.props;
    const { salesExperience } = this.state;
    return (
      <Grid item>
        <Typography
          style={{ color: formLabelColor }}
          gutterBottom
          variant={inputLabelTextVariant}
        >
          Experiência em vendas (se houver)
        </Typography>
        <FormControl fullWidth>
          <TextField
            variant="outlined"
            label="Digite sua experiência em vendas"
            fullWidth
            value={salesExperience || ""}
            onChange={(event) =>
              this.setState({
                salesExperience: event.target.value,
              })
            }
          />
        </FormControl>
      </Grid>
    );
  }

  renderReferralInput() {
    const { i18n } = this.props;
    const { referral } = this.state;
    return (
      <Grid item>
        <Typography
          style={{ color: formLabelColor }}
          gutterBottom
          variant={inputLabelTextVariant}
        >
          Indicação ou como nos conheceu (se houver)
        </Typography>
        <FormControl fullWidth>
          <TextField
            variant="outlined"
            label="Digite sua indicação ou como nos conheceu"
            fullWidth
            value={referral || ""}
            onChange={(event) =>
              this.setState({
                referral: event.target.value,
              })
            }
          />
        </FormControl>
      </Grid>
    );
  }

  renderBankNameInput() {
    const { i18n } = this.props;
    const { bankName } = this.state;
    return (
      <Grid item>
        <Typography
          style={{ color: formLabelColor }}
          gutterBottom
          variant={inputLabelTextVariant}
        >
          Nome do banco
        </Typography>
        <FormControl required fullWidth>
          <TextField
            variant="outlined"
            required
            label="Digite sua nome do banco"
            fullWidth
            value={bankName || ""}
            onChange={(event) =>
              this.setState({
                bankName: event.target.value,
              })
            }
            error={bankName && !this.validate("bankName", bankName)}
          />
        </FormControl>
      </Grid>
    );
  }

  renderAccountNumberInput() {
    const { i18n } = this.props;
    const { accountNumber } = this.state;
    return (
      <Grid item>
        <Typography
          style={{ color: formLabelColor }}
          gutterBottom
          variant={inputLabelTextVariant}
        >
          CC Conta Bancária
        </Typography>
        <FormControl required fullWidth>
          <TextField
            variant="outlined"
            required
            label="Digite sua CC conta bancária "
            fullWidth
            value={accountNumber || ""}
            onChange={(event) =>
              this.setState({
                accountNumber: event.target.value,
              })
            }
            error={
              accountNumber && !this.validate("accountNumber", accountNumber)
            }
          />
        </FormControl>
      </Grid>
    );
  }

  renderAgencyInput() {
    const { i18n } = this.props;
    const { agency } = this.state;
    return (
      <Grid item>
        <Typography
          style={{ color: formLabelColor }}
          gutterBottom
          variant={inputLabelTextVariant}
        >
          Agência
        </Typography>
        <FormControl required fullWidth>
          <TextField
            variant="outlined"
            required
            label="Digite sua agência"
            fullWidth
            value={agency || ""}
            onChange={(event) =>
              this.setState({
                agency: event.target.value,
              })
            }
            error={agency && !this.validate("agency", agency)}
          />
        </FormControl>
      </Grid>
    );
  }

  renderBankCPFCNPJInput() {
    const { i18n } = this.props;
    const { bankTaxNumber, type } = this.state;
    return (
      <Grid item>
        <Typography
          style={{ color: formLabelColor }}
          gutterBottom
          variant={inputLabelTextVariant}
        >
          {type === "personal" ? "CPF" : "CPF ou CNPJ"}
        </Typography>
        <FormControl required fullWidth>
          <TextField
            variant="outlined"
            required
            label={`Digite sua ${type === "personal" ? "CPF" : "CPF ou CNPJ"}`}
            fullWidth
            value={bankTaxNumber || ""}
            onChange={(event) =>
              this.setState({
                bankTaxNumber: event.target.value,
              })
            }
            error={
              bankTaxNumber && !this.validate("bankTaxNumber", bankTaxNumber)
            }
          />
        </FormControl>
      </Grid>
    );
  }

  renderPixInput() {
    const { i18n } = this.props;
    const { pix } = this.state;
    return (
      <Grid item>
        <Typography
          style={{ color: formLabelColor }}
          gutterBottom
          variant={inputLabelTextVariant}
        >
          Chave PIX (se houver)
        </Typography>
        <FormControl fullWidth>
          <TextField
            variant="outlined"
            label="Digite sua chave PIX"
            fullWidth
            value={pix || ""}
            onChange={(event) =>
              this.setState({
                pix: event.target.value,
              })
            }
            error={pix && !this.validate("pix", pix)}
          />
        </FormControl>
      </Grid>
    );
  }

  renderLastNameInput() {
    const { i18n } = this.props;
    const { lastName } = this.state;
    return (
      <Grid item>
        <Typography
          style={{ color: formLabelColor }}
          gutterBottom
          variant={inputLabelTextVariant}
        >
          {i18n.t("lastName", "Last Name")}
        </Typography>
        <FormControl required fullWidth>
          <TextField
            variant="outlined"
            required
            label={i18n.t("enterYourLastName", "Enter your last name")}
            fullWidth
            value={lastName || ""}
            onChange={(event) =>
              this.setState({
                lastName: event.target.value,
              })
            }
            error={lastName && !this.validate("lastName", lastName)}
          />
        </FormControl>
      </Grid>
    );
  }

  renderTelefoneInput() {
    const { i18n } = this.props;
    const { telefone } = this.state;
    return (
      <Grid item>
        <Typography
          style={{ color: formLabelColor }}
          gutterBottom
          variant={inputLabelTextVariant}
        >
          {i18n.t("telephone", "Phone")}
        </Typography>
        <FormControl required fullWidth>
          <TextField
            variant="outlined"
            required
            label={i18n.t("enterYourTelephone", "Enter your phone number")}
            fullWidth
            value={telefone || ""}
            onChange={(event) =>
              this.setState({
                telefone: event.target.value,
              })
            }
            InputProps={{
              inputComponent: PhoneFormat,
            }}
            error={telefone && !this.validate("telefone", telefone)}
          />
        </FormControl>
      </Grid>
    );
  }

  renderPasswordInput() {
    const { i18n } = this.props;
    const { hidePassword, password } = this.state;
    const passwordErrors =
      password && password.length >= 4 && owasp.test(password).errors;
    let ptPasswordError = null;
    if (passwordErrors?.length > 0) {
      const englishPasswordError = passwordErrors[0];
      ptPasswordError = owaspTranslations[String(englishPasswordError)] || null;
    }

    return (
      <Grid item>
        <Typography
          style={{ color: formLabelColor }}
          gutterBottom
          variant={inputLabelTextVariant}
        >
          {i18n.t("password", "Password")}
        </Typography>
        <FormControl required fullWidth>
          <TextField
            variant="outlined"
            required
            label={i18n.t("enterYourPassword", "Enter your password")}
            fullWidth
            onChange={(event) =>
              this.setState({
                password: event.target.value,
              })
            }
            type={hidePassword ? "password" : "text"}
            value={password || ""}
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    color="primary"
                    style={{ marginRight: "-11px" }}
                    aria-label="toggle password visibility"
                    onClick={() => {
                      this.setState({ hidePassword: !hidePassword });
                    }}
                    onMouseDown={(event) => event.preventDefault()}
                  >
                    {hidePassword ? (
                      <VisibilityOffOutlined />
                    ) : (
                      <VisibilityOutlined />
                    )}
                  </IconButton>
                </InputAdornment>
              ),
            }}
            autoComplete="password"
            error={!!ptPasswordError}
            helperText={ptPasswordError}
          />
        </FormControl>
      </Grid>
    );
  }
  // renderTermsOfService() {
  //   const { readTerms, password, errors } = this.state;
  //   return (
  //     <Collapse
  //       in={!password || password.length <= 5 || !errors || errors.length === 0}
  //     >
  //       <FormControl
  //         style={{ marginLeft: fullScreen ? 0 : ".6rem" }}
  //         margin="dense"
  //         required
  //         fullWidth
  //       >
  //         <FormControlLabel
  //           control={
  //             <Checkbox
  //               required
  //               style={{ marginRight: "4px" }}
  //               checked={readTerms}
  //               onChange={() => {
  //                 this.setState({ readTerms: !readTerms });
  //               }}
  //             />
  //           }
  //           style={{ marginRight: 0 }}
  //           label={
  //             <FormHelperText style={{ width: "100%", lineHeight: "1.4" }}>
  //               Eu li e concordo com os
  //               <a
  //                 // onClick={e => e.preventDefault()}
  //                 className={classes.aTagBtn}
  //                 target="_blank"
  //                 rel="noopener noreferrer"
  //                 href={constants.termsOfServiceUrl}
  //               >
  //                 {" "}
  //                 termos de uso
  //               </a>{" "}
  //               e{" "}
  //               <a
  //                 // onClick={e => e.preventDefault()}
  //                 className={classes.aTagBtn}
  //                 target="_blank"
  //                 rel="noopener noreferrer"
  //                 href={constants.privacyPolicyUrl}
  //               >
  //                 política de privacidade
  //               </a>
  //               .
  //             </FormHelperText>
  //           }
  //         />
  //       </FormControl>
  //     </Collapse>
  //   );
  // }

  renderEmailInput() {
    const { email } = this.state;
    return (
      <Grid item>
        <Typography
          style={{ color: formLabelColor }}
          gutterBottom
          variant={inputLabelTextVariant}
        >
          E-mail
        </Typography>
        <FormControl required fullWidth>
          <TextField
            variant="outlined"
            required
            label={i18n.t("enterYourEmail", "Enter your email")}
            fullWidth
            value={email || ""}
            onChange={(event) =>
              this.setState({
                email: event.target.value,
              })
            }
            error={email && !this.validate("email", email)}
          />
        </FormControl>
      </Grid>
    );
  }

  renderStep0() {
    const { type } = this.state;
    const { i18n } = this.props;

    const title =
      type === "personal"
        ? i18n.t(
            "enterPersonalContactInformation",
            "Enter your personal contact information"
          )
        : i18n.t(
            "enterBusinessInformation",
            "Enter your business's information"
          );

    return (
      <Grid container>
        <Grid
          item
          xs={12}
          style={{
            paddingTop: "1rem",
            paddingBottom: "2rem",
          }}
        >
          <Typography style={{ fontWeight: "bold" }}>{title}</Typography>
        </Grid>
        <Grid item xs={12}>
          <Grid container direction="column" spacing={2}>
            {type === "personal" ? (
              <>
                {this.renderFirstNameInput()}
                {this.renderLastNameInput()}
                {this.renderCPFInput()}
              </>
            ) : (
              <>
                {this.renderCompanyLegalNameInput()}
                {this.renderCompanyNameInput()}
                {this.renderCNPJInput()}
                {this.renderTelefoneInput()}
                {this.renderPlainTextInput({
                  stateAttr: "jobTitle",
                  label: "Cargo na empresa",
                  prompt: `Cargo na empresa`,
                  required: true,
                })}
              </>
            )}
          </Grid>
        </Grid>
        {this.renderActionButtons()}
      </Grid>
    );
  }

  renderStep1() {
    const { i18n } = this.props;
    const { type } = this.state;
    const title =
      type === "personal"
        ? i18n.t(
            "enterPersonalContactInformation",
            "Enter your personal contact information"
          )
        : i18n.t(
            "legalRepresentativeInformation",
            "Legal Representative Information"
          );
    return (
      <Grid container>
        <Grid
          item
          xs={12}
          style={{
            paddingTop: "1rem",
            paddingBottom: "2rem",
          }}
        >
          <Typography style={{ fontWeight: "bold" }}>{title}</Typography>
        </Grid>
        <Grid item xs={12}>
          <Grid container direction="column" spacing={2}>
            {type === "personal" ? (
              <>
                {this.renderTelefoneInput()}
                {this.renderPlainTextInput({
                  stateAttr: "address1",
                  label: i18n.t("address1", "Address 1"),
                  prompt: `Digite o ${i18n.t("address1", "Address 1")}`,
                  required: true,
                })}
                {this.renderPlainTextInput({
                  stateAttr: "address2",
                  label: i18n.t("address2", "Address 2"),
                  prompt: `Digite o ${i18n.t("address2", "Address 2")}`,
                  required: false,
                })}
                {this.renderPlainTextInput({
                  stateAttr: "district",
                  label: i18n.t("district", "District"),
                  prompt: `Digite o ${i18n.t("district", "District")}`,
                  required: true,
                })}
                {this.renderPlainTextInput({
                  stateAttr: "city",
                  label: i18n.t("city", "City"),
                  prompt: `Digite o ${i18n.t("city", "City")}`,
                  required: true,
                })}
                {this.renderPlainTextInput({
                  stateAttr: "state",
                  label: i18n.t("state", "State"),
                  prompt: `Digite o ${i18n.t("state", "State")}`,
                  required: true,
                })}
                {this.renderPlainTextInput({
                  stateAttr: "postalCode",
                  label: i18n.t("postalCode", "Postal code"),
                  prompt: `Digite o ${i18n.t("postalCode", "postal code")}`,
                  required: true,
                })}
              </>
            ) : (
              <>
                {this.renderFirstNameInput()}
                {this.renderLastNameInput()}
                {this.renderCPFInput()}
                {this.renderPlainTextInput({
                  stateAttr: "address1",
                  label: i18n.t("address1", "Address 1"),
                  prompt: `Digite o ${i18n.t("address1", "Address 1")}`,
                  required: true,
                })}
                {this.renderPlainTextInput({
                  stateAttr: "address2",
                  label: i18n.t("address2", "Address 2"),
                  prompt: `Digite o ${i18n.t("address2", "Address 2")}`,
                  required: false,
                })}
                {this.renderPlainTextInput({
                  stateAttr: "district",
                  label: i18n.t("district", "District"),
                  prompt: `Digite o ${i18n.t("district", "District")}`,
                  required: true,
                })}
                {this.renderPlainTextInput({
                  stateAttr: "city",
                  label: i18n.t("city", "City"),
                  prompt: `Digite o ${i18n.t("city", "City")}`,
                  required: true,
                })}
                {this.renderPlainTextInput({
                  stateAttr: "state",
                  label: i18n.t("state", "State"),
                  prompt: `Digite o ${i18n.t("state", "State")}`,
                  required: true,
                })}
                {this.renderPlainTextInput({
                  stateAttr: "postalCode",
                  label: i18n.t("postalCode", "Postal code"),
                  prompt: `Digite o ${i18n.t("postalCode", "postal code")}`,
                  required: true,
                })}
              </>
            )}
          </Grid>
        </Grid>
        {this.renderActionButtons()}
      </Grid>
    );
  }

  renderStep2() {
    const { i18n } = this.props;
    const { type } = this.state;

    const title =
      type === "personal"
        ? i18n.t(
            "enterPersonalContactInformation",
            "Enter your personal contact information"
          )
        : i18n.t(
            "enterBusinessInformation",
            "Enter your business's information"
          );

    return (
      <Grid container>
        <Grid
          item
          xs={12}
          style={{
            paddingTop: "1rem",
            paddingBottom: "2rem",
          }}
        >
          <Typography style={{ fontWeight: "bold" }}>{title}</Typography>
        </Grid>
        <Grid item xs={12}>
          <Grid container direction="column" spacing={2}>
            {type === "personal" ? (
              <>{this.renderOperatingRegionInput()}</>
            ) : (
              <>{this.renderOperatingRegionInput()}</>
            )}
          </Grid>
        </Grid>
        {this.renderActionButtons()}
      </Grid>
    );
  }

  renderStep3() {
    const { i18n } = this.props;
    const { type } = this.state;

    const title =
      type === "personal"
        ? i18n.t(
            "enterPersonalContactInformation",
            "Enter your personal contact information"
          )
        : i18n.t(
            "enterBusinessInformation",
            "Enter your business's information"
          );

    return (
      <Grid container>
        <Grid
          item
          xs={12}
          style={{
            paddingTop: "1rem",
            paddingBottom: "2rem",
          }}
        >
          <Typography style={{ fontWeight: "bold" }}>{title}</Typography>
        </Grid>
        <Grid item xs={12}>
          <Grid container direction="column" spacing={2}>
            {type === "personal" ? (
              <>
                {this.renderSalesExperienceInput()}
                {this.renderReferralInput()}
              </>
            ) : (
              <>
                {this.renderSalesExperienceInput()}
                {this.renderReferralInput()}
              </>
            )}
          </Grid>
        </Grid>
        {this.renderActionButtons()}
      </Grid>
    );
  }

  renderStep4() {
    const { i18n } = this.props;
    const { type } = this.state;

    const title =
      type === "personal"
        ? i18n.t(
            "enterYourBankingInformation",
            "Enter your banking information for commission payments"
          )
        : i18n.t(
            "enterYourBankingInformation",
            "Enter your banking information for commission payments"
          );

    return (
      <Grid container>
        <Grid
          item
          xs={12}
          style={{
            paddingTop: "1rem",
            paddingBottom: "2rem",
          }}
        >
          <Typography style={{ fontWeight: "bold" }}>{title}</Typography>
        </Grid>
        <Grid item xs={12}>
          <Grid container direction="column" spacing={2}>
            {type === "personal" ? (
              <>
                {this.renderBankNameInput()}
                {this.renderAccountNumberInput()}
                {this.renderAgencyInput()}
                {this.renderBankCPFCNPJInput()}
                {this.renderPixInput()}
              </>
            ) : (
              <>
                {this.renderBankNameInput()}
                {this.renderAccountNumberInput()}
                {this.renderAgencyInput()}
                {this.renderBankCPFCNPJInput()}
                {this.renderPixInput()}
              </>
            )}
          </Grid>
        </Grid>
        {this.renderActionButtons()}
      </Grid>
    );
  }

  renderStep5() {
    const { i18n } = this.props;
    const { type, photoIdCloudinaryPublicId } = this.state;

    const title =
      type === "personal"
        ? "Envie uma foto do seu documento de identificação pessoal"
        : "Envie uma foto do seu documento de identificação pessoal";

    return (
      <Grid container>
        <Grid
          item
          xs={12}
          style={{
            paddingTop: "1rem",
            paddingBottom: "2rem",
          }}
        >
          <Typography style={{ fontWeight: "bold" }}>{title}</Typography>
        </Grid>
        <Grid item xs={12}>
          <Grid container direction="column" spacing={2}>
            {type === "personal" ? (
              <>
                <Button
                  onClick={this.uploadImageWithCloudinary}
                  variant="contained"
                  style={{ marginLeft: "1rem" }}
                  disabled={!!photoIdCloudinaryPublicId}
                  color="primary"
                  endIcon={<Add />}
                >
                  {photoIdCloudinaryPublicId
                    ? "Documento enviado"
                    : "Enviar documento"}
                </Button>
              </>
            ) : (
              <>
                <Button
                  onClick={this.uploadImageWithCloudinary}
                  variant="contained"
                  style={{ marginLeft: "1rem" }}
                  disabled={!!photoIdCloudinaryPublicId}
                  color="primary"
                  endIcon={<Add />}
                >
                  {photoIdCloudinaryPublicId
                    ? "Documento enviado"
                    : "Enviar documento"}
                </Button>
              </>
            )}
          </Grid>
        </Grid>
        {this.renderActionButtons()}
      </Grid>
    );
  }

  renderStep6() {
    const { i18n } = this.props;
    const { type } = this.state;

    const title =
      type === "personal"
        ? i18n.t(
            "registerByCreatingAnEmailAndPassword",
            "Register by Creating an Email and Password"
          )
        : i18n.t(
            "registerByCreatingAnEmailAndPassword",
            "Register by Creating an Email and Password"
          );

    return (
      <Grid container>
        <Grid
          item
          xs={12}
          style={{
            paddingTop: "1rem",
            paddingBottom: "2rem",
          }}
        >
          <Typography style={{ fontWeight: "bold" }}>{title}</Typography>
        </Grid>
        <Grid item xs={12}>
          <Grid container direction="column" spacing={2}>
            {type === "personal" ? (
              <>
                {this.renderEmailInput()}
                {this.renderPasswordInput()}
              </>
            ) : (
              <>
                {this.renderEmailInput()}
                {this.renderPasswordInput()}
              </>
            )}
          </Grid>
        </Grid>
        {this.renderActionButtons()}
      </Grid>
    );
  }

  validate(attr, val) {
    switch (attr) {
      case "firstName":
      case "lastName":
      case "bankName":
      case "accountNumber":
      case "agency":
      case "bankTaxNumber":
      case "companyLegalName":
      case "companyName":
      case "address1":
      case "city":
      case "state":
      case "postalCode":
      case "district":
      case "jobTitle":
        return val && val.length >= 2;
      case "cpf":
        return cpf.isValid(val);
      case "cnpj":
        return cnpj.isValid(val);
      case "email":
        return validator.isEmail(val);
      case "password":
        return val && owasp.test(val).errors.length === 0;
      case "telefone":
        return validator.isMobilePhone(val, "pt-BR");
      case "dob":
        return moment(val).isValid() && moment(val).isBefore(moment());
      case "operatingRegions":
        return val && val.length > 0;
      default:
        return true;
    }
  }

  getContinueDisabled() {
    const {
      activeStep,
      type,
      cpf,
      cnpj,
      firstName,
      lastName,
      telefone,
      email,
      password,
      companyName,
      companyLegalName,
      operatingRegions,
      bankName,
      accountNumber,
      agency,
      bankTaxNumber,
      address1,
      city,
      state,
      district,
      postalCode,
      photoIdCloudinaryPublicId,
      loading,
      jobTitle,
    } = this.state;
    switch (activeStep) {
      case 0: {
        if (type === "personal") {
          return (
            firstName === null ||
            !this.validate("firstName", firstName) ||
            lastName === null ||
            !this.validate("lastName", lastName) ||
            cpf === null ||
            !this.validate("cpf", cpf)
          );
        }
        if (type === "business") {
          return (
            cnpj === null ||
            !this.validate("cnpj", cnpj) ||
            jobTitle === null ||
            !this.validate("jobTitle", jobTitle) ||
            companyLegalName === null ||
            !this.validate("companyLegalName", companyLegalName) ||
            companyName === null ||
            !this.validate("companyName", companyName) ||
            telefone === null ||
            !this.validate("telefone", telefone)
          );
        }
        return true;
      }
      case 1:
        if (type === "personal") {
          return (
            telefone === null ||
            !this.validate("telefone", telefone) ||
            address1 === null ||
            !this.validate("address1", address1) ||
            district === null ||
            !this.validate("district", district) ||
            city === null ||
            !this.validate("city", city) ||
            postalCode === null ||
            !this.validate("postalCode", postalCode) ||
            state === null ||
            !this.validate("state", state)
          );
        }
        if (type === "business") {
          return (
            firstName === null ||
            !this.validate("firstName", firstName) ||
            lastName === null ||
            !this.validate("lastName", lastName) ||
            cpf === null ||
            !this.validate("cpf", cpf) ||
            address1 === null ||
            !this.validate("address1", address1) ||
            district === null ||
            !this.validate("district", district) ||
            city === null ||
            !this.validate("city", city) ||
            postalCode === null ||
            !this.validate("postalCode", postalCode) ||
            state === null ||
            !this.validate("state", state)
          );
        }
        return true;
      case 2:
        if (type === "personal") {
          return (
            operatingRegions === null ||
            !this.validate("operatingRegions", operatingRegions)
          );
        }
        if (type === "business") {
          return (
            operatingRegions === null ||
            !this.validate("operatingRegions", operatingRegions)
          );
        }
        return true;
      case 3:
        if (type === "personal") {
          return false;
        }
        if (type === "business") {
          return false;
        }
        return true;
      case 4:
        if (type === "personal") {
          return (
            bankName === null ||
            !this.validate("bankName", bankName) ||
            bankTaxNumber === null ||
            !this.validate("bankTaxNumber", bankTaxNumber) ||
            agency === null ||
            !this.validate("agency", agency) ||
            accountNumber === null ||
            !this.validate("accountNumber", accountNumber)
          );
        }
        if (type === "business") {
          return (
            bankName === null ||
            !this.validate("bankName", bankName) ||
            bankTaxNumber === null ||
            !this.validate("bankTaxNumber", bankTaxNumber) ||
            agency === null ||
            !this.validate("agency", agency) ||
            accountNumber === null ||
            !this.validate("accountNumber", accountNumber)
          );
        }
        return true;
      case 5:
        if (type === "personal") {
          return photoIdCloudinaryPublicId === null;
        }
        if (type === "business") {
          return photoIdCloudinaryPublicId === null;
        }
        return true;
      case 6:
        if (type === "personal") {
          return (
            loading ||
            email === null ||
            !this.validate("email", email) ||
            password === null ||
            !this.validate("password", password)
          );
        }
        if (type === "business") {
          return (
            loading ||
            email === null ||
            !this.validate("email", email) ||
            password === null ||
            !this.validate("password", password)
          );
        }
        return true;
      default:
        return false;
    }
  }

  render() {
    const { fullScreen, theme, i18n } = this.props;
    const { activeStep, type } = this.state;

    return (
      // <Dialog fullScreen open={true}>
      //   <DialogContent style={{ padding: 0 }}>
      <Grid container style={{ background: "#fff" }}>
        <Grid item container>
          <Hidden smDown>
            <Grid
              style={{
                backgroundSize: "cover",
                // height: "100%",
                position: "relative",
                backgroundImage:
                  "url(https://res.cloudinary.com/energea/image/upload/v1654724323/energea/br-images/solar-panel-in-perspective-2022-03-04-01-48-32-utc_1.webp)",
              }}
              item
              md={4}
              xs={false}
            >
              <Grid
                container
                style={{ marginTop: "2.5rem", width: "100%" }}
                justifyContent="center"
              >
                <Grid item>
                  <Grid
                    container
                    style={{ width: "100%" }}
                    direction="column"
                    justifyContent="space-between"
                    alignItems="space-between"
                  >
                    <Grid item>
                      <img
                        style={{
                          position: "absolute",
                          top: 40,
                          right: -81,
                        }}
                        src="https://res.cloudinary.com/energea/image/upload/v1654724967/energea/br-images/selo.webp"
                        alt="Selo"
                      />
                      {/* <Link to="/" className="no-select">
                        <img
                          style={{
                            marginRight: "1.5rem",
                            marginTop: "-6px",
                            verticalAlign: "middle",
                          }}
                          width={constants.headerLogoWidth}
                          src={constants.headerLogoUrl}
                          alt="Logo"
                        />
                      </Link> */}
                    </Grid>
                    <Grid item style={{ padding: "0 4rem 0 1rem" }}>
                      <Typography
                        variant="h1"
                        style={{
                          fontSize: "32px",
                          fontWeight: 400,
                          color: "#fff",
                          width: "100%",
                        }}
                      >
                        Torne-se um
                      </Typography>
                      <Typography
                        variant="h1"
                        style={{
                          color: theme.palette.green1.main,
                          fontSize: "36px",
                          lineHeight: "135%",
                        }}
                      >
                        Parceiro Energea!
                      </Typography>
                    </Grid>
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
          </Hidden>
          <Grid
            item
            md={8}
            xs={12}
            style={{
              padding: "2rem 1rem",
              // height: "100vh",
              position: "relative",
            }}
          >
            <Grid container justifyContent="center">
              <Grid item md={9} xs={12}>
                <Typography
                  gutterBottom
                  color="primary"
                  variant="h2"
                  style={{ marginBottom: "2rem" }}
                >
                  {i18n.t("signUp", "Sign Up")}
                </Typography>
                <Collapse in={type === null}>
                  <Grid container style={{ width: "100%" }}>
                    <Grid
                      item
                      xs={12}
                      style={{
                        padding: fullScreen ? "1rem 0" : "2rem 0",
                      }}
                    >
                      <Typography
                        style={{
                          textAlign: "left",
                          fontWeight: "bold",
                        }}
                        variant={fullScreen ? "body1" : "h6"}
                      >
                        Selecione o tipo de cadastro que deseja realizar
                      </Typography>
                    </Grid>
                    <Grid item xs={12} style={{ textAlign: "center" }}>
                      <Grid container justifyContent="center" spacing={1}>
                        <Grid item xs={12} md={6} lg={4}>
                          <CardSelector
                            primaryText="Pessoa Física"
                            onClick={() => {
                              this.setState({ type: "personal" });
                            }}
                            icon={<i className="fa-light fa-person" />}
                          />
                        </Grid>
                        <Grid item xs={12} md={6} lg={4}>
                          <CardSelector
                            primaryText="Pessoa Jurídica"
                            onClick={() => {
                              this.setState({ type: "business" });
                            }}
                            icon={<i className="fa-light fa-sharp fa-city" />}
                          />
                        </Grid>
                      </Grid>
                    </Grid>
                  </Grid>
                </Collapse>
                <Collapse
                  in={type === "personal" || type === "business"}
                  style={{ width: "100%" }}
                >
                  <Stepper
                    style={{ paddingTop: 0 }}
                    alternativeLabel
                    activeStep={activeStep}
                  >
                    {[1, 2, 3, 4, 5, 6, 7].map((step) => (
                      <Step key={String(step)}>
                        <StepLabel />
                      </Step>
                    ))}
                  </Stepper>
                  <Grid container justifyContent="center">
                    <Grid
                      item
                      container
                      xs={12}
                      lg={10}
                      justifyContent="center"
                    >
                      {this.renderStepper()}
                    </Grid>
                  </Grid>
                </Collapse>
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    );
  }
}

export default withAuth(
  withSnackbar(
    graphql(isAuthenticatedQuery, {
      name: "isAuthenticatedData",
      options: {
        skip: true, // Skip initial query since we're only using refetch
      },
      props: ({ data }) => ({
        isAuthenticatedRefetch: data?.refetch,
      }),
    })(
      graphql(getAuth0TokensMutation, {
        name: "getAuth0Tokens",
      })(
        graphql(selfRegisterSalesPartnerMutation, {
          name: "selfRegisterSalesPartner",
        })(
          graphql(logToSlackMutation, {
            name: "logToSlack",
          })(
            withStyles(styles, { withTheme: true })(
              withMobileDialog()(withTranslator(Register)),
              {
                whiteBackground: true,
              }
            )
          )
        )
      )
    )
  )
);
