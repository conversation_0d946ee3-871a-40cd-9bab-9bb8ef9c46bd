import React from "react";
import { Grid } from "@material-ui/core";

const withPageMargin = (WrappedComponent, options) => {
  const HOC = (props) => {
    return (
      <Grid
        container
        justifyContent="center"
        style={{
          backgroundColor:
            options && options.whiteBackground ? "#fff" : "inherit",
          padding: "2rem 1rem",
        }}
      >
        <Grid item xl={8} lg={9} md={10} sm={11} xs={12}>
          <WrappedComponent {...props} />
        </Grid>
      </Grid>
    );
  };
  return HOC;
};

export default withPageMargin;
