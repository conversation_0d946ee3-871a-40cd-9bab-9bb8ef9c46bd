import React from "react";
import { graphql } from "react-apollo";

import { loader } from "graphql.macro";
import { useAuth0 } from "@auth0/auth0-react";
import useReliableAuth from "../../hooks/useReliableAuth";

const requiredAuthQuery = loader("../../queries/RequiredAuth.graphql");
// test
const withRequiredAuth = (WrappedComponent) => {
  const HOC = (props) => {
    const { logout } = useAuth0();
    const { isAuthenticated } = useReliableAuth();
    if (
      isAuthenticated &&
      props?.data &&
      !props.data.loading &&
      !props.data?.me?.brContact
    ) {
      // Could logout user if they don't have required permissions
      logout({
        logoutParams: {
          returnTo: `${window.location.origin}`,
        },
      });
    }
    return <WrappedComponent {...props} />;
  };
  return graphql(requiredAuthQuery)(HOC);
};

export default withRequiredAuth;
