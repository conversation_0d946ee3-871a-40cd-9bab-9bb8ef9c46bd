import React from "react";
import { useAuth0 } from "@auth0/auth0-react";

const withAuth = (WrappedComponent) => (props) => {
  const authState = useAuth0();
  const { isAuthenticated, isLoading, getIdTokenClaims } = authState;

  if (isLoading) return null;

  return (
    <WrappedComponent
      {...props}
      accessToken={localStorage.getItem("id_token")}
      authState={{
        isAuthenticated,
        isLoading,
        ...authState,
      }}
      auth0={authState}
      getIdTokenClaims={getIdTokenClaims}
    />
  );
};
export default withAuth;
