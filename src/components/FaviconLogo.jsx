import React from "react";
import { withStyles } from "@material-ui/core/styles";

import { ReactComponent as FaviconLogoSvg } from "../assets/energea_favicon_logo.svg";

const styles = (theme) => ({
  // FaviconLogo color switching styles
  "favicon-logo-dark": {
    "& .logo-text": {
      fill: `${theme.palette.white.main} !important`,
    },
  },
  "favicon-logo-light": {
    "& .logo-text": {
      fill: `${theme.palette.primary.main} !important`,
    },
  },
  "favicon-logo-leaf": {
    "& .logo-leaf": {
      fill: `${theme.palette.secondary.main} !important`,
    },
  },
});

const FaviconLogo = ({ classes, darkMode = false, width = "41px", ...props }) => {
  const logoClasses = [
    classes["favicon-logo-leaf"], // Always apply leaf styling
    darkMode ? classes["favicon-logo-dark"] : classes["favicon-logo-light"], // Apply text color based on mode
  ]
    .filter(Boolean)
    .join(" ");

  return (
    <FaviconLogoSvg
      className={logoClasses}
      style={{
        verticalAlign: "middle",
        width,
        height: "auto",
      }}
      alt="Energea Favicon Logo"
      title="Energea - Renewable Energy Investing for All"
      {...props}
    />
  );
};

export default withStyles(styles)(FaviconLogo);
