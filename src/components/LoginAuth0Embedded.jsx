import React, { useEffect, useRef } from "react";
import { Navigate } from "react-router-dom";
import Auth0Lock from "auth0-lock";
import { useAuth0 } from "@auth0/auth0-react";
import { withStyles } from "@material-ui/core/styles";

import withSnackbar from "./hocs/withSnackbar";
import withTranslator from "./hocs/withTranslator";

const styles = (theme) => ({
  container: {
    background: "#fff",
    height: "100vh",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    padding: theme.spacing(2),
  },
  lockContainer: {
    width: "100%",
    maxWidth: "400px",
    minHeight: "500px",
  },
});

const Login = (props) => {
  const { isAuthenticated, isLoading } = useAuth0();
  const { classes } = props;
  const lockRef = useRef(null);
  const containerRef = useRef(null);

  useEffect(() => {
    if (!isLoading && !isAuthenticated && containerRef.current) {
      // Check if Auth0 environment variables are set
      const clientId = process.env.REACT_APP_AUTH0_CLIENT_ID;
      const domain = process.env.REACT_APP_AUTH0_DOMAIN;

      if (!clientId || !domain) {
        console.error("Auth0 environment variables not set:", {
          REACT_APP_AUTH0_CLIENT_ID: clientId,
          REACT_APP_AUTH0_DOMAIN: domain,
        });
        // Show error message to user
        if (containerRef.current) {
          containerRef.current.innerHTML = `
            <div style="text-align: center; padding: 2rem; color: #f44336;">
              <h3>Authentication Configuration Error</h3>
              <p>Auth0 environment variables are not configured.</p>
              <p>Please set REACT_APP_AUTH0_CLIENT_ID and REACT_APP_AUTH0_DOMAIN</p>
            </div>
          `;
        }
        return;
      }

      // Initialize Auth0 Lock widget
      const lock = new Auth0Lock(clientId, domain, {
        container: "auth0-lock-container",
        auth: {
          redirectUrl: `${window.location.origin}/auth/callback`,
          responseType: "token id_token",
          params: {
            scope: "openid profile email",
          },
        },
        theme: {
          primaryColor: "#1976d2", // Material-UI primary color
          logo: "https://res.cloudinary.com/energea/image/upload/q_auto/v1643042463/energea/energealogosquare.jpg",
        },
        languageDictionary: {
          title: "Energea Brasil",
          signUpTerms:
            "By signing up, you agree to our terms of service and privacy policy.",
        },
        allowSignUp: false, // Only allow login, not registration
        allowForgotPassword: true,
        closable: false,
        autoclose: true,
        rememberLastLogin: true,
        socialButtonStyle: "big",
        oidcConformant: true,
        allowedConnections: ["Username-Password-Authentication"],
      });

      // Show the lock widget
      lock.show();
      lockRef.current = lock;

      // Handle successful authentication
      lock.on("authenticated", (authResult) => {
        // Store the token
        if (authResult.idToken) {
          localStorage.setItem("id_token", authResult.idToken);
          if (authResult.expiresIn) {
            const expiresAt = Date.now() + authResult.expiresIn * 1000;
            localStorage.setItem("id_token_expires_at", expiresAt.toString());
          }
        }
        // Redirect to dashboard
        window.location.href = "/dashboard?welcome=1";
      });

      // Handle authentication errors
      lock.on("authorization_error", (error) => {
        console.error("Auth0 Lock authentication error:", error);
      });

      // Handle unrecoverable errors
      lock.on("unrecoverable_error", (error) => {
        console.error("Auth0 Lock unrecoverable error:", error);
      });
    }

    // Cleanup function
    return () => {
      if (lockRef.current) {
        lockRef.current.hide();
        lockRef.current = null;
      }
    };
  }, [isLoading, isAuthenticated]);

  if (isLoading) return null;
  if (isAuthenticated) return <Navigate to="/dashboard" />;

  return (
    <div className={classes.container}>
      <div className={classes.lockContainer}>
        <div ref={containerRef} id="auth0-lock-container" />
      </div>
    </div>
  );
};

export default withSnackbar(
  withStyles(styles, { withTheme: true })(withTranslator(Login))
);
