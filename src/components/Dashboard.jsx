import React, { useState, useEffect } from "react";
import { graphql, Query } from "react-apollo";
import { useAuth0 } from "@auth0/auth0-react";
import copy from "copy-to-clipboard";
import validator from "validator";
import {
  Button,
  Chip,
  CircularProgress,
  Collapse,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Grid,
  List,
  ListItem,
  ListItemText,
  Paper,
  Typography,
  useTheme,
  withStyles,
  Divider,
  withMobileDialog,
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  TablePagination,
  TextField,
  Tooltip,
  MenuItem,
  FormControl,
  Select,
  // ListItemSecondaryAction,
} from "@material-ui/core";
import numeral from "numeral";
import moment from "moment";

import {
  Add,
  Close,
  CloudDownload,
  Edit,
  Email,
  FileCopy,
  Info,
  SwapHoriz,
} from "@material-ui/icons";

import { loader } from "graphql.macro";
import { Alert } from "@material-ui/lab";

import client from "../apollo";
import PerformanceChart from "./PerformanceChart";
import InvoiceList from "./InvoiceList";
import CommissionSimulator from "./CommissionSimulator";
import UserManagementDialog from "./UserManagementDialog";
import ConsumerUnitCreateForm from "./ConsumerUnitCreateForm";
import { formatCurrency } from "../lib/formatting";
import withTranslator from "./hocs/withTranslator";
import withSnackbar from "./hocs/withSnackbar";
import PartnerConsumerUnitUploadDialog from "./PartnerConsumerUnitUploadDialog";
import PartnerConsumerUnitCreateForm from "./PartnerConsumerUnitCreateForm";
import PartnerConsumerUnitInfoDialog from "./PartnerConsumerUnitInfoDialog";
import SalesPersonCreateDialog from "./SalesPersonCreateDialog";
import { Line } from "react-chartjs-2";
import { getGradient } from "../global";

const dashboardQuery = loader("../queries/Dashboard.graphql");
const consumerUnitsQuery = loader("../queries/PartnerConsumerUnits.graphql");
const salesPartnerSalesPeopleQuery = loader(
  "../queries/PartnerSalesPeople.graphql"
);
const brConsumerUnitStagesQuery = loader(
  "../queries/BrConsumerUnitStages.graphql"
);
const downloadConsumerUnitExcelQuery = loader(
  "../queries/BrConsumerUnitExcelDownload.graphql"
);
const logToSlackMutation = loader("../mutations/LogToSlack.graphql");
const updateBrTermsOfAdhesionMutation = loader(
  "../mutations/UpdateBrTermsOfAdhesion.graphql"
);
const createBrTermsOfAdhesionFormMutation = loader(
  "../mutations/CreateBrTermsOfAdhesionForm.graphql"
);
const sendRegisterConsumerUnitEmailMutation = loader(
  "../mutations/SendRegisterLinkEmail.graphql"
);
const updateBrConsumerUnitMutation = loader(
  "../mutations/UpdateBrConsumerUnit.graphql"
);

const styles = (theme) => ({
  sections: {
    padding: theme.spacing(4),
    borderTop: "5px solid #fff",
  },
  sectionTitleDividers: {
    width: "100%",
    margin: "1rem 0",
  },
});

const formLabelColor = "rgba(0, 0, 0, 0.54)";

const Dashboard = (props) => {
  const { isAuthenticated, isLoading, logout } = useAuth0();
  const { data, logToSlack, classes } = props;

  // Enhanced logout function that clears all tokens
  const enhancedLogout = (options = {}) => {
    // Clear our localStorage tokens
    localStorage.removeItem("id_token");
    localStorage.removeItem("id_token_expires_at");

    // Clear session storage flags
    sessionStorage.removeItem("auth0_login_redirect");
    sessionStorage.removeItem("auth0_login_time");
    sessionStorage.removeItem("dashboard_visited");

    // Call Auth0 logout with proper redirect
    logout({
      logoutParams: {
        returnTo: `${window.location.origin}/login`,
        ...options.logoutParams,
      },
      ...options,
    });
  };

  useEffect(() => {
    if (isLoading) {
      return (
        <Grid
          container
          component={Paper}
          className={classes.sections}
          justifyContent="center"
          alignItems="center"
          style={{ minHeight: "300px" }}
        >
          <CircularProgress />
        </Grid>
      );
    }
    if (!isAuthenticated) {
      // Check if this is manual authentication - if so, don't logout immediately
      const isManualAuth = sessionStorage.getItem("manual_auth_success");
      if (isManualAuth) {
        return; // Don't logout, give Auth0 state time to catch up
      }

      enhancedLogout();
    }
    if (isAuthenticated && !data.loading && !data?.me?.brContact) {
      enhancedLogout();
    }
    // Handle case where isAuthenticated is true but data.me is missing (token issue)
    if (isAuthenticated && !data.loading && !data?.me) {
      enhancedLogout();
    }

    // Clean up manual auth flag when authentication is stable
    if (isAuthenticated && !data.loading && data?.me?.brContact) {
      const isManualAuth = sessionStorage.getItem("manual_auth_success");
      if (isManualAuth) {
        sessionStorage.removeItem("manual_auth_success");
      }
    }
  }, [
    isAuthenticated,
    isLoading,
    data.loading,
    data?.me?.brContact,
    data?.me,
    logout,
  ]); // Dependencies for useEffect

  if (data.loading || data.error) {
    if (data.error) {
      logToSlack({
        variables: {
          input: {
            title: "Error fetching dashboard data (energea.com.br)",
            type: "platform-error",
            data: [
              {
                label: "Error Message",
                value: JSON.stringify(data.error),
              },
            ],
          },
        },
      });
    }
    return (
      <Grid
        container
        component={Paper}
        className={classes.sections}
        justifyContent="center"
        alignItems="center"
        style={{ minHeight: "300px" }}
      >
        <CircularProgress />
      </Grid>
    );
  }

  // If data.me is missing, the useEffect will handle logout, so just show loading
  if (!data.me) {
    return (
      <Grid
        container
        component={Paper}
        className={classes.sections}
        justifyContent="center"
        alignItems="center"
        style={{ minHeight: "300px" }}
      >
        <CircularProgress />
      </Grid>
    );
  }
  const {
    me: { brContact },
  } = data;
  if (!brContact) {
    // logToSlack({
    //   variables: {
    //     input: {
    //       title: "No BrContact returned on dashboard (energea.com.br)",
    //       type: "platform-error",
    //       data: [
    //         {
    //           label: "Data",
    //           value: JSON.stringify(data),
    //         },
    //         {
    //           label: "Email",
    //           value: JSON.stringify(authState?.idToken?.claims?.email),
    //         },
    //       ],
    //     },
    //   },
    // });
    return null;
  }

  const isSalesPerson = !!brContact.brSalesPerson;
  if (isSalesPerson) {
    return <SalesPersonDashboard {...props} />;
  }
  return <CustomerDashboard {...props} />;
};

const SalesPersonDashboard = (props) => {
  const theme = useTheme();
  // const [expansionPanelOpen, setExpansionPanelOpen] = useState(true);
  const {
    classes,
    data,
    i18n,
    snackbar,
    logToSlack,
    updateBrTermsOfAdhesion,
    createBrTermsOfAdhesionForm,
  } = props;
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchStringInstallationCode, setSearchStringInstallationCode] =
    useState("");
  const [searchStringCustomerName, setSearchStringCustomerName] = useState("");
  const [searchStringCustomer, setSearchStringCustomer] = useState("");
  const [filterSalesPersonId, setFilterSalesPersonId] = useState(null);
  const [filterToaStatus, setFilterToaStatus] = useState("all");
  const [filterStatusId, setFilterStatusId] = useState(null);
  const [linkSelectedPowerPlanId, setLinkSelectedPowerPlanId] = useState(null);
  const [sendRegisterLinkEmailDialogOpen, setSendRegisterLinkEmailDialogOpen] =
    useState(false);
  const [consumerUnitCreateDialogOpen, setConsumerUnitCreateDialogOpen] =
    useState(false);
  const [email, setEmail] = useState("");
  const [emailError, setEmailError] = useState(false);
  const [partnerNotesDialogOpenId, setPartnerNotesDialogOpenId] =
    useState(null);
  const [notes, setNotes] = useState(null);
  const [cuInvoiceListDialogOpenId, setCuInvoiceListDialogOpenId] =
    useState(null);
  const [createSalesPersonDialogOpen, setCreateSalesPersonDialogOpen] =
    useState(false);
  const [consumerUnitDownloadLoading, setConsumerUnitDownloadLoading] =
    useState(false);

  const brContact = data?.me?.brContact;
  const brSalesPerson = brContact?.brSalesPerson;
  const isAdminContact =
    brSalesPerson?.adminBrContact?.id &&
    brSalesPerson?.adminBrContact?.id === brContact?.id;
  const hasEarnedCommission =
    brContact?.brCommissionPaymentChartData?.length > 0 &&
    brContact.brCommissionPaymentChartData[
      brContact.brCommissionPaymentChartData.length - 1
    ]?.value > 0;
  const totalCommissionEarned =
    brContact?.brCommissionPaymentChartData?.length > 0
      ? brContact.brCommissionPaymentChartData[
        brContact.brCommissionPaymentChartData.length - 1
      ]?.value
      : 0;

  const lintedPowerPlans = brContact.brPowerPlans?.filter(
    (powerPlan) =>
      powerPlan.utilityCompany &&
      powerPlan.brTariffClass &&
      powerPlan.discountRate !== null &&
      // powerPlan.salesPersonUpfrontCommissionRate !== null &&
      powerPlan.salesPersonResidualCommissionRate !== null &&
      // powerPlan.adminUpfrontCommissionRate !== null &&
      powerPlan.adminResidualCommissionRate !== null
  );

  if (!linkSelectedPowerPlanId && lintedPowerPlans.length > 0) {
    setLinkSelectedPowerPlanId(lintedPowerPlans[0].id);
  }

  const handleSendRegisterLinkEmail = (email, link) => {
    const { sendRegisterConsumerUnitEmail } = props;
    sendRegisterConsumerUnitEmail({
      variables: {
        input: {
          email,
          link,
        },
      },
    }).then(() => {
      setSendRegisterLinkEmailDialogOpen(false);
      setEmail("");
      setEmailError(false);
    });
  };

  const renderCreateCUButton = () => (
    <Button
      variant="contained"
      color="secondary"
      startIcon={<Add />}
      onClick={() => {
        setConsumerUnitCreateDialogOpen(true);
      }}
    >
      {i18n.t("addConsumerUnit", "Add Consumer Unit")}
    </Button>
  );

  const renderCreateConsumerUnitDialog = () => {
    return (
      <Dialog open={!!consumerUnitCreateDialogOpen} fullScreen>
        <DialogTitle>
          <Grid container justifyContent="space-between" alignItems="center">
            <Grid item>
              <Grid container direction="column">
                <Grid item>
                  <Typography variant="h5">
                    {i18n.t("addConsumerUnit", "Add Consumer Unit")}
                  </Typography>
                </Grid>
              </Grid>
            </Grid>
            <Grid item>
              <IconButton
                aria-label="close"
                onClick={() => {
                  setConsumerUnitCreateDialogOpen(false);
                }}
              >
                <Close />
              </IconButton>
            </Grid>
          </Grid>
        </DialogTitle>
        <DialogContent>
          <PartnerConsumerUnitCreateForm
            onClose={() => {
              setConsumerUnitCreateDialogOpen(false);
            }}
            brSalesPersonId={brSalesPerson.id}
            salesPersonBrContactId={brContact.id}
            brPowerPlans={lintedPowerPlans}
          />
        </DialogContent>
      </Dialog>
    );
  };

  const renderWelcomeComponent = (includeAddCUButton) => (
    <Grid
      container
      justifyContent="space-between"
      alignItems="center"
      style={{ marginBottom: "1rem" }}
      spacing={2}
    >
      <Grid item>
        <Typography variant="h5">
          <b>
            {i18n.t("welcome", "Welcome")}, {brContact.fullName}
          </b>
        </Typography>
        <Typography
          variant="caption"
          style={{ color: theme.palette.text.grey }}
        >
          Membro desde{" "}
          {new Date(brContact.createdAt).toLocaleDateString("pt-br", {
            month: "short",
            day: "numeric",
            year: "numeric",
          })}
        </Typography>
      </Grid>
      {includeAddCUButton && <Grid item>{renderCreateCUButton()}</Grid>}
    </Grid>
  );

  const renderCommissionSection = () => (
    <Grid
      container
      component={Paper}
      className={classes.sections}
      style={{ marginBottom: "2rem" }}
    >
      <Grid item>
        <Typography variant="h6">
          <b>Comissão</b>
        </Typography>
      </Grid>
      <Divider className={classes.sectionTitleDividers} />
      <Grid item container spacing={2}>
        {hasEarnedCommission && (
          <Grid container item xs={12} direction="column">
            <Grid
              item
              container
              justifyContent="space-between"
              alignItems="center"
            >
              <Grid item>
                <Typography variant="body1">Comissão recebida</Typography>
              </Grid>
              <Grid item>
                <Grid
                  container
                  direction="column"
                  style={{ color: theme.palette.secondary.main }}
                  alignItems="flex-end"
                >
                  <Grid item>
                    <Typography variant="h1">
                      R$
                      {numeral(totalCommissionEarned).format("0[.]0a")}
                    </Typography>
                  </Grid>
                  <Grid item>
                    <Typography variant="body2">
                      <b>comissão recebida</b>
                    </Typography>
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
            <Grid item>
              <Line
                height={300}
                data={{
                  datasets: [
                    {
                      label: `Comissão`,
                      fill: true,
                      backgroundColor: (context) =>
                        getGradient(context, theme.palette.secondary.main),
                      data: brContact.brCommissionPaymentChartData.map(
                        (el) => ({ x: el.date, y: el.value })
                      ),
                      stepped: true,
                      pointRadius: 0,
                      borderWidth: 4,
                      borderColor: theme.palette.secondary.main,
                    },
                  ],
                }}
                options={{
                  maintainAspectRatio: false,
                  plugins: {
                    legend: { display: false },
                    tooltip: {
                      mode: "index",
                      intersect: false,
                      position: "nearest",
                      callbacks: {
                        title: (tooltipItems) => {
                          const tooltipItem = tooltipItems?.[0];
                          if (!tooltipItem) return null;
                          return new Date(
                            tooltipItem.parsed.x
                          ).toLocaleDateString("pt-br", {
                            month: "short",
                            year: "numeric",
                            day: "numeric",
                          });
                        },
                        label: (tooltipItem) =>
                          `${tooltipItem.dataset.label}: ${numeral(
                            tooltipItem.raw.y
                          ).format("$0,0[.]00")}`,
                      },
                    },
                  },
                  scales: {
                    x: {
                      type: "time",
                      time: {
                        unit: "month",
                      },
                      ticks: {
                        callback: (value) =>
                          new Date(value).toLocaleDateString("pt-br", {
                            month: "short",
                            year: "numeric",
                          }),
                      },
                    },
                    y: {
                      beginAtZero: true,
                      title: {
                        text: "Comissão recebida (R$)",
                        display: true,
                      },
                    },
                  },
                }}
              />
            </Grid>
          </Grid>
        )}
        <CommissionSimulator
          currentActiveConsumerUnitCount={
            brContact.activeBrConsumerUnitCount || 0
          }
          currentActiveConsumerUnitAverageConsumption={
            brContact.activeBrConsumerUnitAverageConsumption || 500
          }
        />
      </Grid>
    </Grid>
  );

  const handleDownloadConsumerUnits = () => {
    setConsumerUnitDownloadLoading(true);
    client
      .query({
        query: downloadConsumerUnitExcelQuery,
        variables: {
          sort: {
            field: "createdAt",
            order: "DESC",
          },
          filter: {
            brSalesPerson: { id: brSalesPerson.id },
            salesPersonBrContact: isAdminContact
              ? (filterSalesPersonId && {
                id: filterSalesPersonId,
              }) ||
              null
              : { id: brContact.id },
            brConsumerUnitStage:
              (filterStatusId && {
                id: filterStatusId,
              }) ||
              null,
            installationCode: searchStringInstallationCode,
            brCustomerName: searchStringCustomerName,
            cpfCnpj: searchStringCustomer,
            hasSignedToa:
              filterToaStatus === "all"
                ? null
                : filterToaStatus === "signed"
                  ? true
                  : false,
          },
        },
        fetchPolicy: "no-cache",
      })
      .then((res) => {
        const downloadLink = document.createElement("a");
        downloadLink.download = `unidades_consumidoras.xlsx`;
        downloadLink.href = res.data.brConsumerUnitExcelDownloadUrl;
        document.body.append(downloadLink);
        downloadLink.click();
        document.body.removeChild(downloadLink);
        setConsumerUnitDownloadLoading(false);
        return null;
      });
  }

  const renderConsumerUnitSection = () => (
    <Grid container component={Paper} className={classes.sections}>
      <Grid container justifyContent="space-between" alignItems="center">
        <Grid item>
          <Typography variant="h6">
            <b>{i18n.t("consumerUnits", "Consumer Units")}</b>
          </Typography>
        </Grid>
        <Grid item>
          <Grid container spacing={2}>
            <Grid item>
              <IconButton
                disabled={consumerUnitDownloadLoading}
                onClick={() => {
                  handleDownloadConsumerUnits();
                }}
              >
                <CloudDownload />
              </IconButton>
            </Grid>
            <Grid item>
              {renderCreateCUButton()}
              {renderCreateConsumerUnitDialog()}
            </Grid>
          </Grid>
        </Grid>
        {/* 12/1/2024 - Julia Sauer requested to temporarily remove this button */}
        {/* <Grid item>
                  <Button
                    variant="contained"
                    color="secondary"
                    startIcon={<Add />}
                    onClick={() => {
                      setConsumerUnitCreateDialogOpen(true);
                    }}
                  >
                    {i18n.t("addConsumerUnit", "Add Consumer Unit")}
                  </Button>
                  <PartnerConsumerUnitUploadDialog
                    open={consumerUnitCreateDialogOpen}
                    handleClose={() => {
                      setConsumerUnitCreateDialogOpen(false);
                    }}
                    brSalesPersonId={brSalesPerson.id}
                  />
                </Grid> */}
      </Grid>
      <Divider className={classes.sectionTitleDividers} />
      <Grid container spacing={2}>
        <Grid item>
          <TextField
            label="Código"
            variant="outlined"
            value={searchStringInstallationCode || ""}
            fullWidth
            onChange={(e) => {
              setPage(0);
              setSearchStringInstallationCode(e.target.value);
              // refetch();
            }}
          />
        </Grid>
        <Grid item>
          <TextField
            label="Nome"
            variant="outlined"
            value={searchStringCustomerName || ""}
            fullWidth
            onChange={(e) => {
              setPage(0);
              setSearchStringCustomerName(e.target.value);
              // refetch();
            }}
          />
        </Grid>
        <Grid item>
          <TextField
            label="CPF, CNPJ"
            variant="outlined"
            value={searchStringCustomer || ""}
            fullWidth
            onChange={(e) => {
              setPage(0);
              setSearchStringCustomer(e.target.value);
              // refetch();
            }}
          />
        </Grid>
        <Query query={brConsumerUnitStagesQuery}>
          {({ loading, error, data, refetch }) => {
            if (loading || error) {
              return null;
            }
            return (
              <Grid item>
                <FormControl required fullWidth>
                  <Select
                    fullWidth
                    variant="outlined"
                    id="selecione-status"
                    name="selecione-status"
                    required
                    // label="Test"
                    // labelWidth={50}
                    value={filterStatusId || "all"}
                    onChange={(e) => {
                      setPage(0);
                      if (e.target.value === "all") {
                        setFilterStatusId(null);
                      } else {
                        setFilterStatusId(e.target.value);
                      }
                    }}
                  >
                    <MenuItem key="all-status" name="all-status" value="all">
                      Todos os status
                    </MenuItem>
                    {data?.allBrConsumerUnitStages?.map((status) => (
                      <MenuItem
                        key={`status-${status.id}`}
                        name={`status-${status.id}`}
                        value={status.id}
                      >
                        {status.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
            );
          }}
        </Query>
        <Grid item>
          <FormControl required fullWidth>
            <Select
              fullWidth
              variant="outlined"
              id="selecione-termos-status"
              name="selecione-termos-status"
              required
              // label="Test"
              // labelWidth={50}
              value={filterToaStatus || "all"}
              onChange={(e) => {
                setPage(0);
                setFilterToaStatus(e.target.value);
              }}
            >
              <MenuItem
                key={`termos-status-all`}
                name={`termos-status-all`}
                value="all"
              >
                Todos status dos termos
              </MenuItem>
              <MenuItem
                key={`termos-assinado`}
                name={`termos-assinado`}
                value="signed"
              >
                Assinado
              </MenuItem>
              <MenuItem
                key={`termos-nao-assinado`}
                name={`termos-nao-assinado`}
                value="notsigned"
              >
                Não assinado
              </MenuItem>
            </Select>
          </FormControl>
        </Grid>
        {isAdminContact && (
          <Query
            query={salesPartnerSalesPeopleQuery}
            variables={{
              filter: {
                brSalesPerson: { id: brSalesPerson.id },
              },
              pagination: { page: 1, perPage: 10_000 },
              sort: { field: "lastName", order: "DESC" },
            }}
          >
            {({ loading, error, data, refetch }) => {
              if (loading || error) {
                return null;
              }
              return (
                <Grid item>
                  <FormControl required fullWidth>
                    <Select
                      fullWidth
                      variant="outlined"
                      id="selecione-vendedor"
                      name="selecione-vendedor"
                      required
                      // label="Test"
                      // labelWidth={50}
                      value={filterSalesPersonId || "all"}
                      onChange={(e) => {
                        setPage(0);
                        if (e.target.value === "all") {
                          setFilterSalesPersonId(null);
                        } else {
                          setFilterSalesPersonId(e.target.value);
                        }
                      }}
                    >
                      <MenuItem
                        key="all-sales-people"
                        name="all-sales-people"
                        value="all"
                      >
                        Todos os vendedores
                      </MenuItem>
                      {data?.brContactFeed?.rows?.map((salesPerson) => (
                        <MenuItem
                          key={`sales-person-${salesPerson.id}`}
                          name={`sales-person-${salesPerson.id}`}
                          value={salesPerson.id}
                        >
                          {salesPerson.fullName}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
              );
            }}
          </Query>
        )}
      </Grid>
      <Query
        query={consumerUnitsQuery}
        variables={{
          filter: {
            brSalesPerson: { id: brSalesPerson.id },
            salesPersonBrContact: isAdminContact
              ? (filterSalesPersonId && {
                id: filterSalesPersonId,
              }) ||
              null
              : { id: brContact.id },
            brConsumerUnitStage:
              (filterStatusId && {
                id: filterStatusId,
              }) ||
              null,
            installationCode: searchStringInstallationCode,
            brCustomerName: searchStringCustomerName,
            cpfCnpj: searchStringCustomer,
            hasSignedToa:
              filterToaStatus === "all"
                ? null
                : filterToaStatus === "signed"
                  ? true
                  : false,
          },
          pagination: { page: page + 1, perPage: rowsPerPage },
          sort: { field: "createdAt", order: "DESC" },
        }}
      >
        {({ loading, error, data, refetch }) => {
          if (error || loading)
            return (
              <Grid
                style={{
                  position: "fixed",
                  top: "50%",
                  width: "100%",
                  textAlign: "center",
                }}
              >
                <CircularProgress />
              </Grid>
            );

          const handleSaveConsumerUnitPartnerNotes = () => {
            const { updateBrConsumerUnit } = props;
            updateBrConsumerUnit({
              variables: {
                input: {
                  id: partnerNotesDialogOpenId,
                  salesPartnerNotes: notes,
                },
              },
            }).then(() => {
              setPartnerNotesDialogOpenId(null);
              setNotes(null);
              refetch();
            });
          };

          return (
            <Grid container style={{ marginTop: "1rem" }}>
              {data?.brConsumerUnitFeed?.rows.length > 0 ? (
                <>
                  <TableContainer component={Grid}>
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell>
                            <b>Data criação</b>
                          </TableCell>
                          <TableCell>
                            <b>Código de instalação</b>
                          </TableCell>
                          <TableCell>
                            <b>CNPJ/CPF</b>
                          </TableCell>
                          <TableCell>
                            <b>Razão social/titular</b>
                          </TableCell>
                          <TableCell>
                            <b>Consumo sem disponibilidade</b>
                          </TableCell>
                          <TableCell>
                            <b>Plano</b>
                          </TableCell>
                          <TableCell>
                            <b>Assinatura</b>
                          </TableCell>
                          <TableCell>
                            <b>{i18n.t("status", "Status")}</b>
                          </TableCell>
                          {isAdminContact && (
                            <TableCell>
                              <b>Vendedor</b>
                            </TableCell>
                          )}
                          <TableCell>
                            <b>Vencido</b>
                          </TableCell>
                          <TableCell>
                            <b>Notas</b>
                          </TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {data.brConsumerUnitFeed?.rows?.map(
                          (consumerUnit, index) => {
                            const lastRow =
                              index ===
                              data.brConsumerUnitFeed?.rows?.length - 1;
                            const toa =
                              consumerUnit.currentBrTermsOfAdhesion ||
                              consumerUnit.brTermsOfAdhesions?.[0];

                            const sendUnsignedToaEmail = () => {
                              updateBrTermsOfAdhesion({
                                variables: {
                                  input: {
                                    id: toa.id,
                                    sendUnsignedToaEmail: true,
                                  },
                                },
                              }).then(
                                () => {
                                  refetch();
                                  snackbar.setState({
                                    snackbarMessage: i18n.t(
                                      "theDocumentHasBeenSentForSignature",
                                      "The document has been sent for signature."
                                    ),
                                    snackbarOpen: true,
                                    snackbarVariant: "success",
                                  });
                                },
                                (err) => {
                                  snackbar.setState({
                                    snackbarMessage: i18n.t(
                                      "errorEmailingTermsOfAdhesionPleaseTryAgainLaterOrContactUsAtContatoEnergeaCom",
                                      "Error emailing terms of adhesion. Please try again later or contact <NAME_EMAIL>"
                                    ),
                                    snackbarOpen: true,
                                    snackbarVariant: "error",
                                  });
                                  logToSlack({
                                    variables: {
                                      input: {
                                        title:
                                          "Error emailing terms of adhesion from partner dashboard (energea.com.br)",
                                        type: "credit-management-customers",
                                        data: [
                                          {
                                            label: "Error Message",
                                            value: JSON.stringify(err),
                                          },
                                        ],
                                      },
                                    },
                                  });
                                }
                              );
                            };

                            const toaStatusJsx = [];
                            if (toa && toa.signatureDt) {
                              toaStatusJsx.push("Realizado");
                            } else if (
                              toa &&
                              !toa.signatureDt &&
                              toa.pendingAwsObjectKey &&
                              toa.signatureRequestedDt
                            ) {
                              // toaStatusJsx.push("Pendente");
                              toaStatusJsx.push(
                                <Button
                                  variant="contained"
                                  color="secondary"
                                  onClick={(event) => {
                                    event.preventDefault();
                                    event.stopPropagation();
                                    sendUnsignedToaEmail();
                                  }}
                                >
                                  <Grid
                                    container
                                    direction="column"
                                    alignItems="center"
                                    justifyContent="center"
                                  >
                                    <Grid item>
                                      {i18n.t(
                                        "resendTermsOfAdhesion",
                                        "Resend Terms of Adhesion"
                                      )}
                                    </Grid>
                                    <Grid item>
                                      <Typography variant="body2">
                                        Último envio em{" "}
                                        {moment(
                                          toa.signatureRequestedDt
                                        ).format("D/M/YY")}
                                      </Typography>
                                    </Grid>
                                  </Grid>
                                </Button>
                              );
                            } else if (
                              toa &&
                              !toa.signatureDt &&
                              toa.pendingAwsObjectKey &&
                              !toa.signatureRequestedDt
                            ) {
                              toaStatusJsx.push(
                                <Button
                                  variant="contained"
                                  color="secondary"
                                  onClick={(event) => {
                                    event.preventDefault();
                                    event.stopPropagation();
                                    sendUnsignedToaEmail();
                                  }}
                                >
                                  {i18n.t(
                                    "sendTermsOfAdhesion",
                                    "Send Terms of Adhesion"
                                  )}
                                </Button>
                              );
                            } else if (
                              toa &&
                              !toa.signatureDt &&
                              !toa.pendingAwsObjectKey
                            ) {
                              toaStatusJsx.push(
                                <Button
                                  variant="contained"
                                  color="secondary"
                                  onClick={(event) => {
                                    event.preventDefault();
                                    event.stopPropagation();
                                    createBrTermsOfAdhesionForm({
                                      variables: {
                                        input: {
                                          brTermsOfAdhesionId: toa.id,
                                        },
                                      },
                                    }).then(
                                      () => {
                                        sendUnsignedToaEmail();
                                      },
                                      (err) => {
                                        snackbar.setState({
                                          snackbarMessage: i18n.t(
                                            "errorDraftingTermsOfAdhesionPleaseTryAgainLaterOrContactUsAtContatoEnergeaCom",
                                            "Error drafting terms of adhesion. Please try again later or contact <NAME_EMAIL>"
                                          ),
                                          snackbarOpen: true,
                                          snackbarVariant: "error",
                                        });
                                        logToSlack({
                                          variables: {
                                            input: {
                                              title:
                                                "Error drafting terms of adhesion from partner dashboard (energea.com.br)",
                                              type: "credit-management-customers",
                                              data: [
                                                {
                                                  label: "Error Message",
                                                  value: JSON.stringify(err),
                                                },
                                              ],
                                            },
                                          },
                                        });
                                      }
                                    );
                                  }}
                                >
                                  {i18n.t(
                                    "sendTermsOfAdhesion",
                                    "Send Terms of Adhesion"
                                  )}
                                </Button>
                              );
                            } else {
                              toaStatusJsx.push("Não realizado");
                            }
                            return (
                              <TableRow
                                key={consumerUnit.id}
                                hover
                                onClick={(event) => {
                                  event.preventDefault();
                                  event.stopPropagation();
                                  setCuInvoiceListDialogOpenId(consumerUnit.id);
                                }}
                              >
                                <TableCell
                                  style={{
                                    borderBottom: lastRow ? "none" : null,
                                  }}
                                >
                                  {moment(consumerUnit.createdAt).format(
                                    "DD/MM/YYYY"
                                  )}
                                </TableCell>
                                <TableCell
                                  style={{
                                    borderBottom: lastRow ? "none" : null,
                                  }}
                                >
                                  {consumerUnit.installationCode}
                                </TableCell>
                                <TableCell
                                  style={{
                                    borderBottom: lastRow ? "none" : null,
                                  }}
                                >
                                  {consumerUnit.brCustomer.formattedCnpj ||
                                    consumerUnit.brCustomer.formattedCpf}
                                </TableCell>
                                <TableCell
                                  style={{
                                    borderBottom: lastRow ? "none" : null,
                                  }}
                                >
                                  {consumerUnit.brCustomer?.name}
                                </TableCell>
                                <TableCell
                                  style={{
                                    borderBottom: lastRow ? "none" : null,
                                  }}
                                >
                                  {numeral(
                                    consumerUnit.adjustedAvgMonthlyConsumption
                                  ).format("0,0[.]0")}{" "}
                                  kWh
                                </TableCell>
                                <TableCell
                                  style={{
                                    borderBottom: lastRow ? "none" : null,
                                  }}
                                >
                                  {toa?.brPowerPlan?.name}
                                </TableCell>
                                <TableCell
                                  style={{
                                    borderBottom: lastRow ? "none" : null,
                                  }}
                                >
                                  {toaStatusJsx}
                                </TableCell>
                                <TableCell
                                  style={{
                                    borderBottom: lastRow ? "none" : null,
                                  }}
                                >
                                  {consumerUnit.brConsumerUnitStage?.name}
                                </TableCell>
                                {isAdminContact && (
                                  <TableCell
                                    style={{
                                      borderBottom: lastRow ? "none" : null,
                                    }}
                                  >
                                    {
                                      consumerUnit.salesPersonBrContact
                                        ?.fullName
                                    }
                                  </TableCell>
                                )}
                                <TableCell
                                  style={{
                                    borderBottom: lastRow ? "none" : null,
                                  }}
                                >
                                  <Typography
                                    variant="body2"
                                    style={{
                                      fontWeight:
                                        consumerUnit.pastDue > 0
                                          ? "bold"
                                          : "regular",
                                      color:
                                        consumerUnit.pastDue > 0
                                          ? theme.palette.error.light
                                          : undefined,
                                    }}
                                  >
                                    {numeral(consumerUnit.pastDue).format(
                                      "$0[.]00"
                                    )}
                                  </Typography>
                                </TableCell>
                                <TableCell
                                  style={{
                                    borderBottom: lastRow ? "none" : null,
                                  }}
                                >
                                  <Tooltip
                                    title={
                                      consumerUnit.salesPartnerNotes ||
                                      "Clique aqui para adicionar notas."
                                    }
                                  >
                                    <IconButton
                                      size="large"
                                      color="primary"
                                      onClick={(event) => {
                                        event.preventDefault();
                                        event.stopPropagation();
                                        setNotes(
                                          consumerUnit.salesPartnerNotes
                                        );
                                        setPartnerNotesDialogOpenId(
                                          consumerUnit.id
                                        );
                                      }}
                                    >
                                      <Edit />
                                    </IconButton>
                                  </Tooltip>
                                  <Dialog
                                    open={!!partnerNotesDialogOpenId}
                                    onClose={() =>
                                      setPartnerNotesDialogOpenId(null)
                                    }
                                  >
                                    <Grid
                                      container
                                      justifyContent="space-between"
                                      alignItems="center"
                                    >
                                      <Grid item>
                                        <DialogTitle>Notas</DialogTitle>
                                      </Grid>
                                      <Grid item>
                                        <IconButton
                                          onClick={() =>
                                            setPartnerNotesDialogOpenId(null)
                                          }
                                        >
                                          <Close />
                                        </IconButton>
                                      </Grid>
                                    </Grid>
                                    <DialogContent>
                                      <Grid
                                        container
                                        justifyContent="center"
                                        spacing={3}
                                      >
                                        <Grid item xs={12}>
                                          <TextField
                                            required
                                            style={{
                                              background:
                                                theme.palette.white.main,
                                            }}
                                            variant="outlined"
                                            label="Notas"
                                            value={notes || ""}
                                            onChange={(e) => {
                                              setNotes(e.target.value);
                                            }}
                                            fullWidth
                                            multiline
                                          />
                                        </Grid>
                                      </Grid>
                                    </DialogContent>
                                    <DialogActions>
                                      <Button
                                        color="secondary"
                                        variant="contained"
                                        onClick={() => {
                                          handleSaveConsumerUnitPartnerNotes();
                                        }}
                                      >
                                        Salvar
                                      </Button>
                                    </DialogActions>
                                  </Dialog>
                                </TableCell>
                              </TableRow>
                            );
                          }
                        )}
                      </TableBody>
                    </Table>
                    <TablePagination
                      rowsPerPageOptions={[10, 25, 50]}
                      rowsPerPage={rowsPerPage}
                      component="div"
                      count={data.brConsumerUnitFeed?.count || 0}
                      page={page}
                      onPageChange={(event, page) => setPage(page)}
                      onRowsPerPageChange={(event) => {
                        setPage(0);
                        setRowsPerPage(event.target.value);
                      }}
                      labelRowsPerPage={i18n.t("rowsPerPage", "Rows per page")}
                      labelDisplayedRows={({ from, to, count }) =>
                        `${from}-${to} ${i18n.t("of", "of")} ${count}`
                      }
                    // classes={
                    //   fullScreen
                    //     ? {
                    //         actions: classes.paginationActions,
                    //         toolbar: classes.paginationToolbar,
                    //         selectRoot: classes.paginationSelectRoot,
                    //       }
                    //     : {}
                    // }
                    />
                  </TableContainer>
                  {cuInvoiceListDialogOpenId && (
                    <PartnerConsumerUnitInfoDialog
                      open={!!cuInvoiceListDialogOpenId}
                      brConsumerUnitId={cuInvoiceListDialogOpenId}
                      brConsumerUnitName={
                        data.brConsumerUnitFeed?.rows?.find(
                          (cu) => cu.id === cuInvoiceListDialogOpenId
                        )?.name
                      }
                      handleClose={() => setCuInvoiceListDialogOpenId(null)}
                    />
                  )}
                </>
              ) : (
                <Alert severity="info">
                  {i18n.t("noConsumerUnits", "No consumer units")}
                </Alert>
              )}
            </Grid>
          );
        }}
      </Query>
    </Grid>
  );

  const getSalesPartnerStatusMessage = (statusId) => {
    const emailLink = (
      <a href="mailto:<EMAIL>"><EMAIL></a>
    );
    const whatsAppLink = (
      <a href="https://wa.me/552138282002">(21) 3828-2002</a>
    );
    switch (statusId) {
      case 1:
        return (
          <Typography variant="body1">
            Nossa equipe está trabalhando na validação dos dados que você
            forneceu e enviará um contrato para assinatura em até 7 dias após o
            envio das suas informações. Se você tiver alguma dúvida, entre em
            contato conosco pelo e-mail {emailLink} ou pelo nosso WhatsApp{" "}
            {whatsAppLink}.
          </Typography>
        );
      case 3:
        return (
          <Typography variant="body1">
            Sua conta foi reprovada. Se você tiver alguma dúvida ou acreditar
            que isso ocorreu por engano, entre em contato conosco pelo e-mail{" "}
            {emailLink} ou pelo nosso WhatsApp {whatsAppLink}.
          </Typography>
        );
      case 4:
        return (
          <Typography variant="body1">
            Sua conta foi cancelada. Se você tiver alguma dúvida ou acreditar
            que isso ocorreu por engano, entre em contato conosco pelo e-mail{" "}
            {emailLink} ou pelo nosso WhatsApp {whatsAppLink}.
          </Typography>
        );
      default:
        return null;
    }
  };

  const kpis = [
    {
      label: "Total de kWh integrados",
      value: brContact.totalActiveAvgMonthlyConsumptionKwh,
      renderValue: (val) => (
        <Typography variant="h1" color="secondary">
          {numeral(val).format("0,0")}
          <span style={{ fontSize: "1.5rem" }}>kWh</span>
        </Typography>
      ),
    },
    {
      label: "Total em atraso dos clientes",
      value: brContact.totalConsumerUnitOverdueInvoiceAmount,
      renderValue: (val) => (
        <Typography variant="h1" color="secondary">
          <span style={{ fontSize: "1.5rem" }}>R$</span>
          {numeral(val).format("0,0")}
        </Typography>
      ),
    },
  ];

  return (
    <>
      <Grid
        container
        justifyContent="center"
        // alignItems="center"
        style={{ background: theme.palette.primary.main, padding: "2rem 1rem" }}
      >
        <Grid item xl={9} lg={10} md={12} xs={12}>
          {!brSalesPerson?.brSalesPartnerStatus ||
            brSalesPerson?.brSalesPartnerStatus?.id === 2 ? (
            <Grid container>
              <Grid
                container
                component={Paper}
                className={classes.sections}
                style={{ marginBottom: "2rem" }}
              >
                {renderWelcomeComponent(true)}
                <Grid container item xs={12} spacing={2}>
                  <Grid item xs={12} md={4}>
                    <Grid container spacing={3}>
                      {kpis.map((kpi) => (
                        <Grid item container direction="column">
                          <Grid item>
                            <Typography variant="body1">
                              <b>{kpi.label}</b>
                            </Typography>
                          </Grid>
                          <Grid item>{kpi.renderValue(kpi.value)}</Grid>
                        </Grid>
                      ))}
                    </Grid>
                  </Grid>
                  {/* <Collapse in={expansionPanelOpen} style={{ width: "100%" }}> */}
                  <Grid item xs={12} md={8}>
                    <Grid container style={{ width: "100%" }}>
                      <Grid container>
                        <Grid item>
                          <Typography variant="body1">
                            <b>{i18n.t("powerPlans", "Power Plans")}</b>
                          </Typography>
                        </Grid>
                        {/* <Divider className={classes.sectionTitleDividers} /> */}
                        {lintedPowerPlans.length > 0 ? (
                          <TableContainer component={Grid}>
                            <Table size="small">
                              <TableHead>
                                <TableRow>
                                  <TableCell>
                                    <b>
                                      {i18n.t("distributor", "Utility company")}
                                    </b>
                                  </TableCell>
                                  <TableCell>
                                    <b>
                                      {i18n.t("tariffClass", "Tariff class")}
                                    </b>
                                  </TableCell>
                                  <TableCell align="right">
                                    <b>
                                      {i18n.t("discountRate", "Discount rate")}
                                    </b>
                                  </TableCell>
                                  {isAdminContact && (
                                    <>
                                      {/* <TableCell align="right">
                                      <b>
                                        {i18n.t(
                                          "upfrontCommissionRateAdmin",
                                          "Admin upfront commission rate"
                                        )}
                                      </b>
                                    </TableCell> */}
                                      <TableCell align="right">
                                        <b>
                                          {i18n.t(
                                            "commissionRateAdmin",
                                            "Admin commission rate"
                                          )}
                                        </b>
                                      </TableCell>
                                    </>
                                  )}
                                  {/* <TableCell align="right">
                                  <b>
                                    {i18n.t(
                                      "upfrontCommissionRateSalesPerson",
                                      "Sales person upfront commission rate"
                                    )}
                                  </b>
                                </TableCell> */}
                                  <TableCell align="right">
                                    <b>
                                      {isAdminContact
                                        ? i18n.t(
                                          "commissionRateSalesPerson",
                                          "Sales person commission rate"
                                        )
                                        : i18n.t(
                                          "commissionRate",
                                          "Commission rate"
                                        )}
                                    </b>
                                  </TableCell>
                                  <TableCell align="right">
                                    <b>Link de compartilhamento</b>
                                  </TableCell>
                                </TableRow>
                              </TableHead>
                              <TableBody>
                                {lintedPowerPlans.map((powerPlan, index) => {
                                  const lastRow =
                                    index === lintedPowerPlans.length - 1;
                                  const customerSignupLink = brSalesPerson
                                    ? `https://www.energea.com.br/register?spid=${brSalesPerson.id}&spid2=${brContact.id}&ppid=${powerPlan.id}`
                                    : null;
                                  return (
                                    <TableRow key={powerPlan.id}>
                                      <TableCell
                                        style={{
                                          borderBottom: lastRow ? "none" : null,
                                        }}
                                      >
                                        {powerPlan.utilityCompany.name}
                                      </TableCell>
                                      <TableCell
                                        style={{
                                          borderBottom: lastRow ? "none" : null,
                                        }}
                                      >
                                        {powerPlan.brTariffClass.name}
                                      </TableCell>
                                      <TableCell
                                        align="right"
                                        style={{
                                          borderBottom: lastRow ? "none" : null,
                                        }}
                                      >
                                        {numeral(powerPlan.discountRate).format(
                                          "0[.]00%"
                                        )}
                                      </TableCell>
                                      {isAdminContact && (
                                        <>
                                          {/* <TableCell
                                          align="right"
                                          style={{
                                            borderBottom: lastRow
                                              ? "none"
                                              : null,
                                          }}
                                        >
                                          {numeral(
                                            powerPlan.adminUpfrontCommissionRate
                                          ).format("0[.]00%")}
                                        </TableCell> */}
                                          <TableCell
                                            align="right"
                                            style={{
                                              borderBottom: lastRow
                                                ? "none"
                                                : null,
                                            }}
                                          >
                                            {numeral(
                                              powerPlan.adminResidualCommissionRate
                                            ).format("0[.]00%")}
                                          </TableCell>
                                        </>
                                      )}
                                      {/* <TableCell
                                      align="right"
                                      style={{
                                        borderBottom: lastRow ? "none" : null,
                                      }}
                                    >
                                      {numeral(
                                        powerPlan.salesPersonUpfrontCommissionRate
                                      ).format("0[.]00%")}
                                    </TableCell> */}
                                      <TableCell
                                        align="right"
                                        style={{
                                          borderBottom: lastRow ? "none" : null,
                                        }}
                                      >
                                        {numeral(
                                          powerPlan.salesPersonResidualCommissionRate
                                        ).format("0[.]00%")}
                                      </TableCell>
                                      <TableCell
                                        align="right"
                                        style={{
                                          borderBottom: lastRow ? "none" : null,
                                          padding: 0,
                                        }}
                                      >
                                        <IconButton
                                          onClick={() => {
                                            copy(customerSignupLink);
                                          }}
                                          disabled={!customerSignupLink}
                                          color="primary"
                                        >
                                          <FileCopy />
                                        </IconButton>
                                        <IconButton
                                          onClick={() => {
                                            setSendRegisterLinkEmailDialogOpen(
                                              true
                                            );
                                          }}
                                          disabled={!customerSignupLink}
                                          color="primary"
                                        >
                                          <Email />
                                        </IconButton>
                                        <Dialog
                                          open={sendRegisterLinkEmailDialogOpen}
                                          onClose={() =>
                                            setSendRegisterLinkEmailDialogOpen(
                                              false
                                            )
                                          }
                                        >
                                          <Grid
                                            container
                                            justifyContent="space-between"
                                            alignItems="center"
                                          >
                                            <Grid item>
                                              <DialogTitle>
                                                Enviar link de registro por
                                                e-mail
                                              </DialogTitle>
                                            </Grid>
                                            <Grid item>
                                              <IconButton
                                                onClick={() =>
                                                  setSendRegisterLinkEmailDialogOpen(
                                                    false
                                                  )
                                                }
                                              >
                                                <Close />
                                              </IconButton>
                                            </Grid>
                                          </Grid>
                                          <DialogContent>
                                            <Grid
                                              container
                                              justifyContent="center"
                                              spacing={3}
                                            >
                                              <Grid item xs={12}>
                                                <TextField
                                                  required
                                                  style={{
                                                    background:
                                                      theme.palette.white.main,
                                                  }}
                                                  variant="outlined"
                                                  label="E-mail"
                                                  value={email || ""}
                                                  onChange={(e) => {
                                                    const val = e.target.value;
                                                    setEmail(val);
                                                    const emailValid =
                                                      validator.isEmail(val);
                                                    setEmailError(
                                                      !emailValid || !val
                                                    );
                                                  }}
                                                  fullWidth
                                                />
                                              </Grid>
                                            </Grid>
                                          </DialogContent>
                                          <DialogActions>
                                            <Button
                                              color="secondary"
                                              variant="contained"
                                              onClick={() => {
                                                handleSendRegisterLinkEmail(
                                                  email,
                                                  customerSignupLink
                                                );
                                              }}
                                              disabled={!!emailError}
                                            >
                                              Enviar
                                            </Button>
                                          </DialogActions>
                                        </Dialog>
                                      </TableCell>
                                    </TableRow>
                                  );
                                })}
                              </TableBody>
                            </Table>
                          </TableContainer>
                        ) : (
                          <Alert severity="info">
                            {i18n.t("noPowerPlans", "No power plans")}
                          </Alert>
                        )}
                      </Grid>
                    </Grid>
                  </Grid>
                  {/* </Collapse> */}
                </Grid>
              </Grid>
              {isAdminContact && (
                <Grid
                  container
                  component={Paper}
                  className={classes.sections}
                  style={{ marginBottom: "2rem" }}
                >
                  <Grid
                    item
                    container
                    justifyContent="space-between"
                    alignItems="center"
                  >
                    <Grid item>
                      <Typography variant="h6">
                        <b>{i18n.t("myTeam", "My team")}</b>
                      </Typography>
                    </Grid>
                    <Grid item>
                      <Button
                        variant="contained"
                        color="secondary"
                        startIcon={<Add />}
                        onClick={() => {
                          setCreateSalesPersonDialogOpen(true);
                        }}
                      >
                        {i18n.t("addSalesPerson", "Add Sales Person")}
                      </Button>
                    </Grid>
                  </Grid>
                  <Divider className={classes.sectionTitleDividers} />
                  <Query
                    query={salesPartnerSalesPeopleQuery}
                    variables={{
                      filter: {
                        brSalesPerson: { id: brSalesPerson.id },
                      },
                      pagination: { page: 1, perPage: 10_000 },
                      sort: { field: "firstName", order: "ASC" },
                    }}
                  >
                    {({ loading, error, data, refetch }) => {
                      if (loading || error) {
                        return null;
                      }
                      if (data?.brContactFeed?.rows.length === 0) {
                        return (
                          <Alert severity="info">
                            {i18n.t("noSalesPeople", "No sales people")}
                          </Alert>
                        );
                      }
                      return (
                        <>
                          <TableContainer component={Grid}>
                            <Table>
                              <TableHead>
                                <TableRow>
                                  <TableCell>
                                    <b>
                                      {i18n.t("salesPerson", "Sales person")}
                                    </b>
                                  </TableCell>
                                  <TableCell>
                                    <b>{i18n.t("email", "E-mail")}</b>
                                  </TableCell>
                                </TableRow>
                              </TableHead>
                              <TableBody>
                                {data.brContactFeed.rows.map(
                                  (salesPerson, index) => {
                                    const lastRow =
                                      index ===
                                      data.brContactFeed.rows.length - 1;
                                    return (
                                      <TableRow key={salesPerson.id}>
                                        <TableCell
                                          style={{
                                            borderBottom: lastRow
                                              ? "none"
                                              : null,
                                          }}
                                        >
                                          {salesPerson.fullName}
                                        </TableCell>
                                        <TableCell
                                          style={{
                                            borderBottom: lastRow
                                              ? "none"
                                              : null,
                                          }}
                                        >
                                          {salesPerson.email}
                                        </TableCell>
                                      </TableRow>
                                    );
                                  }
                                )}
                              </TableBody>
                            </Table>
                          </TableContainer>
                          <SalesPersonCreateDialog
                            open={createSalesPersonDialogOpen}
                            brSalesPersonId={brSalesPerson.id}
                            onClose={() => {
                              setCreateSalesPersonDialogOpen(false);
                              refetch();
                            }}
                          />
                        </>
                      );
                    }}
                  </Query>
                </Grid>
              )}
              {renderCommissionSection()}
              {renderConsumerUnitSection()}
            </Grid>
          ) : (
            <Grid container>
              <Grid
                container
                component={Paper}
                className={classes.sections}
                style={{ marginBottom: "2rem" }}
              >
                {renderWelcomeComponent()}
                <Grid container item xs={12}>
                  <Typography variant="body1" gutterBottom>
                    Sua conta de parceiro de vendas está atualmente com o
                    status: <b>{brSalesPerson.brSalesPartnerStatus.name}</b>.
                  </Typography>
                  {getSalesPartnerStatusMessage(
                    brSalesPerson.brSalesPartnerStatus.id
                  )}
                </Grid>
              </Grid>
            </Grid>
          )}
        </Grid>
      </Grid>
    </>
  );
};

const CustomerDashboard = (props) => {
  const theme = useTheme();
  const [selectedConsumerUnit, setSelectedConsumerUnit] = useState(null);
  const [selectedCustomer, setSelectedCustomer] = useState(null);
  const [manageContactsCustomerId, setManageContactsCustomerId] =
    useState(null);
  const [switchAccountOpen, setSwitchAccountOpen] = useState(false);
  const [consumerUnitCreateDialogOpen, setConsumerUnitCreateDialogOpen] =
    useState(null);
  const { classes, data, i18n } = props;
  const sectionSpacing = 4;
  let curConsumerUnitData;
  const brContact = data?.me?.brContact;

  if (selectedConsumerUnit) {
    brContact.brContactsBrCustomers.forEach((customer) => {
      if (selectedCustomer.id === customer.brCustomer.id) {
        customer.brCustomer.brConsumerUnits.forEach((consumerUnit) => {
          if (selectedConsumerUnit === consumerUnit.id) {
            curConsumerUnitData = consumerUnit;
          }
        });
      }
    });
  }

  const renderDashboardMenu = () => {
    if (!selectedCustomer?.id && brContact.brContactsBrCustomers.length > 0) {
      const defaultCustomer =
        brContact.brContactsBrCustomers.filter(
          (x) => x.brCustomer.brConsumerUnits?.length > 0
        )[0] || brContact.brContactsBrCustomers[0];
      setSelectedCustomer(defaultCustomer.brCustomer);
    }
    let curCustomerData;
    if (selectedCustomer) {
      curCustomerData = brContact.brContactsBrCustomers.find(
        (el) => el.brCustomer.id === selectedCustomer.id
      );
      if (!selectedConsumerUnit) {
        if (curCustomerData.brCustomer.brConsumerUnits?.[0]?.id) {
          setSelectedConsumerUnit(
            curCustomerData.brCustomer.brConsumerUnits?.[0]?.id
          );
        }
      }
    }
    if (!curCustomerData) {
      return null;
    }
    if (
      !curCustomerData.brCustomer.brConsumerUnits.length &&
      consumerUnitCreateDialogOpen === null
    ) {
      // NOTE: Once its set to false, don't automatically open
      setConsumerUnitCreateDialogOpen(true);
    }

    const renderConsumerUnitStage = (consumerUnitStage) => {
      if (
        !consumerUnitStage ||
        !consumerUnitStage.name ||
        !consumerUnitStage.color
      ) {
        return null;
      }
      return (
        <Grid item style={{ marginTop: ".5rem" }}>
          <Chip
            variant="caption"
            style={{
              backgroundColor: consumerUnitStage.color,
              color: theme.palette.white.main,
            }}
            label={consumerUnitStage.name}
          />
        </Grid>
      );
    };

    return (
      <Grid
        container
        component={Paper}
        style={{
          padding: 0,
        }}
      >
        <Grid
          item
          xs={12}
          component={Paper}
          elevation={8}
          // outlined
          style={{
            padding: "1rem",
            border: `solid 5px ${theme.palette.secondary.main}`,
          }}
        >
          <Grid item>
            <Typography>
              <b>
                {i18n.t("welcome", "Welcome")}, {brContact.fullName}
              </b>
            </Typography>
          </Grid>
          <Divider className={classes.sectionTitleDividers} />
          {/* curCustomerData */}
          <Grid item xs={12}>
            <Grid container direction="column">
              <Grid item>
                <Typography gutterBottom variant="body2">
                  <b>{i18n.t("account", "Account")} :</b>{" "}
                  {curCustomerData.brCustomer.name}
                </Typography>
              </Grid>
              {/* <Grid item>
                <Typography gutterBottom variant="body2">
                  <b>{i18n.t("status", "Status")} :</b> Invoices Due
                </Typography>
              </Grid> */}
              <Grid
                container
                justifyContent="space-between"
                style={{ marginTop: "2rem" }}
              >
                {["admin", "owner"].indexOf(curCustomerData.role) > -1 && (
                  <Grid item>
                    <Button
                      variant="outlined"
                      color="primary"
                      size="small"
                      onClick={() =>
                        setManageContactsCustomerId(
                          curCustomerData.brCustomer.id
                        )
                      }
                      startIcon={<i className="fa-solid fa-users"></i>}
                    >
                      {i18n.t("manageUser", "Manage Users")}
                    </Button>
                  </Grid>
                )}
                {brContact.brContactsBrCustomers.length > 1 && (
                  <Grid item>
                    <Button
                      variant="contained"
                      size="small"
                      color="primary"
                      startIcon={<SwapHoriz />}
                      onClick={() => setSwitchAccountOpen(true)}
                    >
                      {i18n.t("switchAccount", "Switch Accounts")}
                    </Button>
                  </Grid>
                )}
              </Grid>
            </Grid>
          </Grid>
        </Grid>
        <Grid item xs={12} style={{ padding: "1rem 1.3rem" }}>
          <Grid container justifyContent="space-between">
            <Grid item>
              <Typography
                gutterBottom
                style={{ fontWeight: "bold", marginTop: "1rem" }}
              >
                <i
                  style={{ marginRight: ".5rem" }}
                  className="fa-solid fa-buildings"
                />
                {i18n.t("consumerUnits", "Consumer Units")}
              </Typography>
            </Grid>
            <Grid item>
              <IconButton
                variant="contained"
                color="primary"
                onClick={() => {
                  setConsumerUnitCreateDialogOpen(true);
                }}
              >
                <Add />
              </IconButton>
            </Grid>
          </Grid>
          <List>
            {curCustomerData.brCustomer.brConsumerUnits.map(
              (consumerUnit, i) => (
                <ListItem
                  button
                  selected={selectedConsumerUnit === consumerUnit.id}
                  divider={
                    i !== curCustomerData.brCustomer.brConsumerUnits.length - 1
                  }
                  style={{ borderRadius: "1rem" }}
                  onClick={() => setSelectedConsumerUnit(consumerUnit.id)}
                >
                  <ListItemText
                    primary={
                      <Typography variant="body2">
                        <b>{consumerUnit.name}</b>
                      </Typography>
                    }
                    secondary={
                      <Grid container direction="column">
                        <Grid item container>
                          {renderConsumerUnitStage(
                            consumerUnit.brConsumerUnitStage
                          )}
                        </Grid>
                        <Grid
                          item
                          container
                          style={{ paddingLeft: ".25rem", marginTop: ".5rem" }}
                          spacing={1}
                        >
                          <Grid item>
                            <i className="fa-solid fa-location-dot"></i>
                          </Grid>
                          <Grid item>
                            <Typography gutterBottom variant="caption">
                              {consumerUnit.address1}
                              <br />
                              {consumerUnit.address2 && (
                                <>
                                  {consumerUnit.address2}
                                  <br />
                                </>
                              )}
                              {consumerUnit.city}, {consumerUnit.district},{" "}
                              {consumerUnit.postalCode}
                            </Typography>
                          </Grid>
                        </Grid>
                      </Grid>
                    }
                  />
                </ListItem>
              )
            )}
          </List>
          <Grid item>
            <Button
              variant="contained"
              color="secondary"
              fullWidth
              onClick={() => {
                setConsumerUnitCreateDialogOpen(true);
              }}
              endIcon={<Add />}
            >
              {i18n.t("addConsumerUnit", "Add Consumer Unit")}
            </Button>
          </Grid>
        </Grid>

        {/* {brContact.brContactsBrCustomers.map((customer) => {
          return (
            <Grid item xs={12}>
              <Grid container direction="column">
                <Grid item>
                  <Typography>{customer.brCustomer.name}</Typography>
                </Grid>
                <Grid item style={{ textAlign: "right", marginTop: ".5rem" }}>
                  {["admin", "owner"].indexOf(customer.role) && (
                    <Button
                      size="small"
                      variant="contained"
                      color="primary"
                      onClick={() =>
                        setManageContactsCustomerId(customer.brCustomer.id)
                      }
                    >
                    {i18n.t("manageUser", "Manage Users")}
                    </Button>
                  )}
                </Grid>
              </Grid>
              <Divider className={classes.sectionTitleDividers} />
              <Typography>Consumer Units</Typography>
              <List>
                {customer.brCustomer.brConsumerUnits.map((consumerUnit, i) => (
                  <ListItem
                    button
                    selected={selectedConsumerUnit === consumerUnit.id}
                    divider={
                      i !== customer.brCustomer.brConsumerUnits.length - 1
                    }
                    style={{ borderRadius: "1rem" }}
                    onClick={() => setSelectedConsumerUnit(consumerUnit.id)}
                  >
                    <ListItemText
                      primary={consumerUnit.name}
                      secondary={
                        <div style={{ paddingLeft: ".5rem" }}>
                          {consumerUnit.address1}
                          <br />
                          {consumerUnit.address2}
                          <br />
                          {consumerUnit.city}, {consumerUnit.district},{" "}
                          {consumerUnit.postalCode}
                        </div>
                      }
                    />
                  </ListItem>
                ))}
              </List>
            </Grid>
          );
        })} */}
      </Grid>
    );
  };

  const renderAccountSummary = () => {
    let jsx;
    if (data.loading) {
      jsx = (
        <Grid
          container
          justifyContent="center"
          alignItems="center"
          style={{ minHeight: "300px" }}
        >
          <CircularProgress />
        </Grid>
      );
    } else {
      jsx = (
        <Grid
          container
          style={{ minHeight: "300px" }}
          justifyContent="space-between"
          direction="column"
        >
          <Grid item>
            <Grid container justifyContent="space-between">
              <Grid item>
                <Typography
                  variant="h5"
                  gutterBottom
                  style={{ color: theme.palette.secondary.main }}
                >
                  <b>{i18n.t("amountDue", "Amount Due")}</b>
                </Typography>
              </Grid>
              <Grid item>
                <Typography
                  variant="h5"
                  gutterBottom
                  style={{ color: theme.palette.secondary.main }}
                >
                  <b>{formatCurrency(curConsumerUnitData?.amountDue || 0)}</b>
                </Typography>
              </Grid>
            </Grid>
            <Grid container justifyContent="space-between">
              <Grid item>
                <Typography gutterBottom variant="body2">
                  {i18n.t("pastDue", "Past Due")}
                </Typography>
              </Grid>
              <Grid item>
                <Typography gutterBottom variant="body2">
                  {formatCurrency(curConsumerUnitData?.pastDue || 0)}
                </Typography>
              </Grid>
            </Grid>
            <Grid container justifyContent="space-between">
              <Grid item>
                <Typography variant="body2" gutterBottom>
                  {i18n.t("currentDue", "Current Due")}
                </Typography>
              </Grid>
              <Grid item>
                <Typography variant="body2" gutterBottom>
                  {formatCurrency(
                    curConsumerUnitData &&
                    curConsumerUnitData.amountDue -
                    curConsumerUnitData.pastDue
                  )}
                </Typography>
              </Grid>
            </Grid>
          </Grid>
          <Grid item>
            <Grid
              container
              alignItems="center"
              direction="column"
              style={{ marginTop: "1rem" }}
            >
              <Grid item>
                <Button
                  component="a"
                  href={
                    curConsumerUnitData?.mostRecentUnpaidBrInvoice
                      ?.stripeInvoice?.invoicePaymentUrl
                  }
                  variant="contained"
                  size="large"
                  disabled={
                    !curConsumerUnitData?.mostRecentUnpaidBrInvoice
                      ?.stripeInvoice?.invoicePaymentUrl
                  }
                  color="secondary"
                  style={{ margin: ".5rem" }}
                >
                  {(curConsumerUnitData?.pastDue || 0) > 0
                    ? i18n.t("payPastDueInvoice", "Pay Past Due Invoice")
                    : i18n.t("makeAPayment", "Make a Payment")}
                </Button>
                {/* <StripeBuyButton
                publishableKey={
                  "pk_test_51P3MsgHSL3dpgWvRFWuEG2p23hH2ZtlaLkPKzi5XZsQM2qAfWjsxPFLyBZiXDcMHIyfwJH1qPIwDYYKp06PCmaCP00rTv0jMSZ"
                }
                buyButtonId={"buy_btn_1P3fjgHSL3dpgWvRQtE2gAfH"}
              /> */}
              </Grid>
              <Grid>
                <Button
                  variant="outlined"
                  size="large"
                  color="secondary"
                  href={
                    curConsumerUnitData?.mostRecentUnpaidBrInvoice
                      ?.stripeInvoice?.invoiceDownloadUrl
                  }
                  disabled={
                    !curConsumerUnitData?.mostRecentUnpaidBrInvoice
                      ?.stripeInvoice?.invoiceDownloadUrl
                  }
                  style={{ margin: ".5rem" }}
                >
                  {i18n.t(
                    "viewLatestUnpaidInvoice",
                    "View Latest Unpaid Invoice"
                  )}
                </Button>
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      );
    }
    return (
      <Grid container component={Paper} className={classes.sections}>
        <Typography variant="body1">
          <b>{i18n.t("accountSummary", "Account Summary")}</b>
        </Typography>
        <Divider className={classes.sectionTitleDividers} />
        {jsx}
      </Grid>
    );
  };

  const renderInvoices = () => {
    // if (data.loading || true) {
    //   return (
    //     <Skeleton variant="rect" style={{ width: "100%", height: "100%" }} />
    //   );
    //   return <Typography>Loading...</Typography>;
    // }
    return (
      <Grid container component={Paper} className={classes.sections}>
        <Typography variant="body1">
          <b>{i18n.t("invoices", "Invoices")}</b>
        </Typography>
        <Divider className={classes.sectionTitleDividers} />
        {selectedConsumerUnit && (
          <InvoiceList brConsumerUnitId={selectedConsumerUnit} />
        )}
      </Grid>
    );
  };
  const renderPerformance = () => {
    return (
      <Grid container component={Paper} className={classes.sections}>
        <Typography variant="body1">
          <b>{i18n.t("performance", "Performance")}</b>
        </Typography>
        <Divider className={classes.sectionTitleDividers} />
        {selectedConsumerUnit ? (
          <PerformanceChart brConsumerUnitId={selectedConsumerUnit} />
        ) : (
          <Grid
            container
            className={classes.sections}
            justifyContent="center"
            alignItems="center"
            style={{ minHeight: "300px" }}
          >
            <CircularProgress />
          </Grid>
        )}
      </Grid>
    );
  };
  const renderAlerts = () => {
    if (data.loading) {
      return null;
    }
    return (
      <Grid
        container
        component={Paper}
        className={classes.sections}
        style={{ marginBottom: "2rem" }}
      >
        <Typography variant="body1">
          <b>Alerts</b>
        </Typography>
        <Divider className={classes.sectionTitleDividers} />
      </Grid>
    );
  };

  const renderSwitchAccountDialog = () => {
    return (
      <Dialog fullScreen={props.fullScreen} open={!!switchAccountOpen}>
        <DialogTitle>
          <Grid container justifyContent="space-between" alignItems="center">
            <Grid item>{i18n.t("switchAccount", "Switch Account")}</Grid>
            <Grid item>
              <IconButton
                aria-label="close"
                onClick={() => {
                  setSwitchAccountOpen(false);
                }}
              >
                <Close />
              </IconButton>
            </Grid>
          </Grid>
        </DialogTitle>
        <DialogContent>
          <List>
            {brContact?.brContactsBrCustomers?.map((customer, i) => (
              <ListItem
                button
                key={`customer-selector-list-item-${customer.id}`}
                selected={selectedCustomer?.id === customer.id}
                // divider={
                //   i !== curCustomerData.brCustomer.brConsumerUnits.length - 1
                // }
                style={{ borderRadius: "1rem" }}
                onClick={() => {
                  setSelectedCustomer(customer.brCustomer);
                  setSelectedConsumerUnit(null);
                }}
              >
                <ListItemText
                  primary={customer.brCustomer.name}
                  secondary={`${customer.brCustomer.brConsumerUnits.length
                    } Consumer Unit${customer.brCustomer.brConsumerUnits.length > 1 ? "s" : ""
                    }`}
                />
              </ListItem>
            ))}
          </List>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setSwitchAccountOpen(false)}>
            {i18n.t("close", "Close")}
          </Button>
        </DialogActions>
      </Dialog>
    );
  };

  const renderCreateConsumerUnitDialog = () => {
    return (
      <Dialog
        open={!!(consumerUnitCreateDialogOpen && selectedCustomer)}
        fullScreen
      >
        <DialogTitle>
          <Grid container justifyContent="space-between" alignItems="center">
            <Grid item>
              <Grid container direction="column">
                <Grid item>
                  <Typography variant="h5">
                    {i18n.t("addConsumerUnit", "Add Consumer Unit")}
                  </Typography>
                </Grid>
                <Grid item>
                  <Typography variant="h6" style={{ fontWeight: "bold" }}>
                    {selectedCustomer?.name}
                  </Typography>
                </Grid>
              </Grid>
            </Grid>
            <Grid item>
              <IconButton
                aria-label="close"
                onClick={() => {
                  setConsumerUnitCreateDialogOpen(false);
                }}
              >
                <Close />
              </IconButton>
            </Grid>
          </Grid>
        </DialogTitle>
        <DialogContent>
          <ConsumerUnitCreateForm
            brCustomerId={selectedCustomer?.id}
            onClose={() => {
              setConsumerUnitCreateDialogOpen(false);
            }}
          />
        </DialogContent>
      </Dialog>
    );
  };

  const showAlerts = false;

  return (
    <>
      <Grid
        container
        justifyContent="center"
        // alignItems="center"
        style={{ background: theme.palette.primary.main, padding: "2rem 1rem" }}
      >
        <Grid item xs={12} lg={10}>
          <Grid container spacing={sectionSpacing}>
            <Grid item xs={12} lg={4}>
              {renderDashboardMenu()}
            </Grid>
            <Grid item xs={12} lg={8}>
              <Collapse in={showAlerts}>{renderAlerts()}</Collapse>
              <Grid container spacing={sectionSpacing}>
                <Grid item xs={12} md={6}>
                  {renderAccountSummary()}
                </Grid>
                <Grid item xs={12} md={6}>
                  {renderInvoices()}
                </Grid>
                {!data.loading && !selectedConsumerUnit ? null : (
                  <Grid item xs={12}>
                    {renderPerformance()}
                  </Grid>
                )}
              </Grid>
            </Grid>
          </Grid>
        </Grid>
      </Grid>
      {manageContactsCustomerId && (
        <UserManagementDialog
          brCustomerId={manageContactsCustomerId}
          open={!!manageContactsCustomerId}
          onClose={() => setManageContactsCustomerId(null)}
        />
      )}
      {renderSwitchAccountDialog()}
      {renderCreateConsumerUnitDialog()}
    </>
  );
};

export default withSnackbar(
  withStyles(styles, { withTheme: true })(
    graphql(logToSlackMutation, { name: "logToSlack" })(
      graphql(updateBrConsumerUnitMutation, { name: "updateBrConsumerUnit" })(
        graphql(createBrTermsOfAdhesionFormMutation, {
          name: "createBrTermsOfAdhesionForm",
        })(
          graphql(updateBrTermsOfAdhesionMutation, {
            name: "updateBrTermsOfAdhesion",
          })(
            graphql(sendRegisterConsumerUnitEmailMutation, {
              name: "sendRegisterConsumerUnitEmail",
            })(
              graphql(dashboardQuery)(
                withMobileDialog()(withTranslator(Dashboard))
              )
            )
          )
        )
      )
    )
  )
);
