import React, { useState } from "react";
import { <PERSON> } from "react-router-dom";
import {
  Avatar,
  Dialog,
  DialogContent,
  Grid,
  Icon,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  ListItemSecondaryAction,
  CircularProgress,
  IconButton,
  DialogTitle,
  Typography,
  Button,
} from "@material-ui/core";
import { Alert } from "@material-ui/lab";
import { loader } from "graphql.macro";
import { graphql } from "react-apollo";
import {
  Add,
  AddCircleOutline,
  Close,
  Email,
  PictureAsPdf,
  WhatsApp,
} from "@material-ui/icons";
import moment from "moment";
import numeral from "numeral";

import withTranslator from "./hocs/withTranslator";
import client from "../apollo";
import ContactCreateDialog from "./ContactCreateDialog";

const consumerUnitQuery = loader(
  "../queries/PartnerConsumerUnitInfoDialog.graphql"
);
const brInvoiceDownloadUrlQuery = loader(
  "../queries/BrInvoiceDownloadUrl.graphql"
);

const PartnerConsumerUnitInfoDialog = (props) => {
  const { data, i18n, open, handleClose, brConsumerUnitName } = props;
  const [downloadInvoiceLoading, setDownloadInvoiceLoading] = useState(false);
  const [createContactDialogOpen, setCreateContactDialogOpen] = useState(false);

  const handleDownloadInvoice = (brInvoiceId) => {
    setDownloadInvoiceLoading(true);
    client
      .query({
        query: brInvoiceDownloadUrlQuery,
        variables: { input: { brInvoiceId, includeStatement: true } },
        fetchPolicy: "no-cache",
      })
      .then(
        (resp) => {
          if (resp.loading) {
            return null;
          }
          if (resp.error) {
            setDownloadInvoiceLoading(false);
            return null;
          }
          if (!resp.data?.brInvoiceDownloadUrl) {
            setDownloadInvoiceLoading(false);
            return null;
          }
          const downloadLink = document.createElement("a");
          downloadLink.download = `fatura.pdf`;
          downloadLink.href = resp.data.brInvoiceDownloadUrl;
          document.body.append(downloadLink);
          downloadLink.click();
          document.body.removeChild(downloadLink);
          setDownloadInvoiceLoading(false);
          return null;
        },
        (err) => {
          setDownloadInvoiceLoading(false);
          return null;
        }
      );
  };

  const renderInvoices = (consumerUnit) => {
    const filteredInvoices = consumerUnit?.recentBrCreditCompensations?.filter(
      (el) => !!el.brInvoice
    );
    return (
      <Grid container direction="column">
        <Grid item>
          <Typography variant="h6">{i18n.t("invoices", "Invoices")}</Typography>
        </Grid>
        <Grid item>
          {!filteredInvoices || filteredInvoices.length === 0 ? (
            <Alert severity="info" style={{ width: "100%" }}>
              {i18n.t("noInvoices", "No Invoices")}
            </Alert>
          ) : (
            <List style={{ width: "100%" }} dense>
              {filteredInvoices.map((creditCompensation) => (
                <ListItem key={`invoice-list-item-${creditCompensation.id}`}>
                  <ListItemAvatar>
                    <Avatar
                      style={{
                        backgroundColor:
                          creditCompensation.brInvoice.paymentStatus.iconColor,
                      }}
                    >
                      <Icon
                        className={
                          creditCompensation.brInvoice.paymentStatus.iconClass
                        }
                        style={{
                          color: "#fff",
                          width: "auto",
                        }}
                      />
                    </Avatar>
                  </ListItemAvatar>
                  <ListItemText
                    primary={`${
                      creditCompensation.brInvoice.brBillingCycle
                        .billingMonthLabelPortuguese
                    } - ${numeral(
                      creditCompensation.brInvoice.amountDue
                    ).format("$0.00")} (Vencimento: ${moment(
                      creditCompensation.brInvoice.dueDt
                    ).format("DD/MM/YYYY")})`}
                    secondary={
                      creditCompensation.brInvoice.paymentStatus.labelPortuguese
                    }
                  />
                  <ListItemSecondaryAction>
                    <IconButton
                      edge="end"
                      aria-label="download"
                      size="small"
                      color="primary"
                      disabled={
                        (!creditCompensation.brInvoice.stripeInvoiceId &&
                          !creditCompensation.brInvoice.starkBankBoletoId) ||
                        downloadInvoiceLoading
                      }
                      onClick={() =>
                        handleDownloadInvoice(creditCompensation.brInvoice.id)
                      }
                    >
                      {downloadInvoiceLoading ? (
                        <CircularProgress style={{ position: "absolute" }} />
                      ) : null}
                      <PictureAsPdf />
                    </IconButton>
                  </ListItemSecondaryAction>
                </ListItem>
              ))}
            </List>
          )}
        </Grid>
      </Grid>
    );
  };

  const renderContacts = (consumerUnit) => {
    const contacts = consumerUnit?.brCustomer?.brContactsBrCustomers?.map(
      (bcbc) => bcbc.brContact
    );
    return (
      <Grid container direction="column">
        <Grid item container alignItems="center">
          <Grid item>
            <Typography variant="h6">
              {i18n.t("contacts", "Contacts")}
            </Typography>
          </Grid>
          <Grid item>
            <IconButton
              color="primary"
              onClick={() => {
                setCreateContactDialogOpen(true);
              }}
            >
              <AddCircleOutline />
            </IconButton>
            <ContactCreateDialog
              open={createContactDialogOpen}
              brCustomerId={consumerUnit?.brCustomer?.id}
              onClose={() => {
                setCreateContactDialogOpen(false);
                props.data.refetch();
              }}
            />
          </Grid>
        </Grid>
        <Grid item>
          {!contacts || contacts.length === 0 ? (
            <Alert severity="info" style={{ width: "100%" }}>
              {i18n.t("noContacts", "No Contacts")}
            </Alert>
          ) : (
            <List style={{ width: "100%" }} dense>
              {contacts.map((contact) => (
                <ListItem key={`contact-list-item-${contact.id}`}>
                  <ListItemText
                    primary={`${contact.fullName}`}
                    secondary={`${contact.email} (${contact.phone})`}
                  />
                  <ListItemSecondaryAction>
                    {contact.whatsAppPhoneLink && (
                      <IconButton
                        edge="end"
                        size="large"
                        color="primary"
                        component={Link}
                        to={contact.whatsAppPhoneLink}
                      >
                        <WhatsApp />
                      </IconButton>
                    )}
                    {contact.email && (
                      <IconButton
                        edge="end"
                        size="large"
                        color="primary"
                        component={Link}
                        to={`mailto:${contact.email}`}
                      >
                        <Email />
                      </IconButton>
                    )}
                  </ListItemSecondaryAction>
                </ListItem>
              ))}
            </List>
          )}
        </Grid>
      </Grid>
    );
  };

  const renderContent = () => {
    if (data.loading) {
      return (
        <Grid
          container
          justifyContent="center"
          alignItems="center"
          style={{ minHeight: "300px" }}
        >
          <CircularProgress />
        </Grid>
      );
    }
    if (data.error) {
      return (
        <Alert severity="error" style={{ width: "100%" }}>
          Apologies! There was an error calculating your accounts performance.
          Please try back later.
        </Alert>
      );
    }

    return (
      <Grid container spacing={2}>
        <Grid item xs={12}>
          {renderContacts(data.consumerUnit)}
        </Grid>
        <Grid item xs={12}>
          {renderInvoices(data.consumerUnit)}
        </Grid>
      </Grid>
    );
  };

  return (
    <Dialog open={open} onClose={() => handleClose()} maxWidth="lg">
      <DialogTitle>
        <Grid container justifyContent="space-between" alignItems="center">
          <Grid item>
            <Grid container direction="column">
              <Grid item>
                <Typography variant="h5">
                  {brConsumerUnitName}
                  {/* {i18n.t("invoices", "Invoices")} */}
                </Typography>
              </Grid>
            </Grid>
          </Grid>
          <Grid item>
            <IconButton
              aria-label="close"
              onClick={() => {
                handleClose();
              }}
            >
              <Close />
            </IconButton>
          </Grid>
        </Grid>
      </DialogTitle>
      <DialogContent>
        <Grid
          container
          justifyContent="space-between"
          direction="column"
          style={{ minHeight: "300px", minWidth: "300px" }}
        >
          <Grid item>{renderContent()}</Grid>
        </Grid>
      </DialogContent>
    </Dialog>
  );
};

export default graphql(consumerUnitQuery)(
  withTranslator(PartnerConsumerUnitInfoDialog)
);
