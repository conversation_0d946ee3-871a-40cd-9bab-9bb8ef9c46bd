import React, { useEffect } from "react";
import { useAuth0 } from "@auth0/auth0-react";
import { useNavigate } from "react-router-dom";
import useReliableAuth from "../hooks/useReliableAuth";
import { Box, CircularProgress, Typography } from "@material-ui/core";

/**
 * Login route component that automatically triggers Auth0 login
 * when someone navigates to /login
 */
const LoginRoute = () => {
  const { loginWithRedirect, isLoading } = useAuth0();
  const { isAuthenticated } = useReliableAuth();
  const navigate = useNavigate();

  useEffect(() => {
    if (isAuthenticated) {
      // If user is already authenticated, redirect to dashboard
      navigate("/dashboard", { replace: true });
    } else if (!isLoading) {
      // If user is not authenticated and Auth0 is not loading, trigger login

      // Set flag and timestamp to indicate user initiated login
      sessionStorage.setItem("auth0_login_redirect", "true");
      sessionStorage.setItem("auth0_login_time", Date.now().toString());

      loginWithRedirect({
        authorizationParams: {
          screen_hint: "login",
          scope: "openid profile email",
          ui_locales: "pt-BR",
        },
        appState: {
          returnTo: "/dashboard?welcome=1",
        },
      });
    }
  }, [isAuthenticated, isLoading, loginWithRedirect, navigate]);

  // Show loading spinner while Auth0 is processing or redirecting
  return (
    <Box
      display="flex"
      flexDirection="column"
      alignItems="center"
      justifyContent="center"
      minHeight="100vh"
      gap={2}
    >
      <CircularProgress size={60} />
      <Typography variant="h6" color="textSecondary">
        {isAuthenticated
          ? "Redirecting to dashboard..."
          : "Redirecting to login..."}
      </Typography>
    </Box>
  );
};

export default LoginRoute;
