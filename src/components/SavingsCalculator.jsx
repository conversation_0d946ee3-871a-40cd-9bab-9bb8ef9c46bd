import React, { Component, Fragment } from "react";
import { Link } from "react-router-dom";
import {
  Button,
  Grid,
  FormHelperText,
  InputLabel,
  MenuItem,
  Paper,
  Select,
  Typography,
  withMobileDialog,
  Slider,
} from "@material-ui/core";

import { withStyles } from "@material-ui/core/styles";
import numeral from "numeral";

import withTranslator from "./hocs/withTranslator";
import { constants } from "../global";

const styles = (theme) => ({});
const CustomSlider = withStyles({
  thumb: {
    height: 20,
    width: 20,
    marginTop: -9,
  },
})(Slider);
class SavingsCalculator extends Component {
  constructor(props) {
    super(props);
    this.state = {
      distributor: "cemig",
      phase: "phase1",
      monthlyCost: 499,
    };
    this.updateMonthlyCost = this.updateMonthlyCost.bind(this);
    this.handleSelectChange = this.handleSelectChange.bind(this);
  }

  getBenefits() {
    const { t } = this.props;
    const returnObj = [];
    for (let index = 1; index <= 8; index++) {
      returnObj.push(t(`savingsCalculator:benefit${index}`));
    }
    return returnObj;
  }

  updateMonthlyCost(event, val) {
    this.setState({ monthlyCost: val });
  }

  getSavings() {
    const { monthlyCost, phase, distributor } = this.state;
    let multiplier = 1;
    let discount;
    if (distributor === "cemig") {
      // discount = 0.15
      discount = constants.discountRate;
      switch (phase) {
        case "phase1":
          multiplier = 0.89;
          break;
        case "phase2":
          multiplier = 0.93;
          break;
        case "phase3":
          multiplier = 0.99;
          break;

        default:
          break;
      }
    } else {
      discount = constants.discountRate;
      switch (phase) {
        case "phase1":
          multiplier = 0.9;
          break;
        case "phase2":
          multiplier = 0.95;
          break;
        case "phase3":
          multiplier = 1.01;
          break;

        default:
          break;
      }
    }

    return monthlyCost * 12 * discount * multiplier;
  }

  handleSelectChange(event, val) {
    this.setState({ [event.target.name]: val.props.value });
  }

  render() {
    const { t, dark, fullScreen } = this.props;
    const { monthlyCost, phase, distributor } = this.state;
    return (
      <Fragment>
        <Typography
          // color="primary"
          style={{
            color: dark ? "#fff" : "inherit",
            marginTop: "2rem",
            marginBottom: "3rem",
          }}
          gutterBottom
          variant="h3"
        >
          {t("linkNames:savingsCalculator")}
        </Typography>
        <Grid container justifyContent="center" item xs={12}>
          <Grid
            style={{ padding: fullScreen ? "1rem" : "2rem" }}
            component={Paper}
            item
            md={6}
            xs={12}
          >
            <Typography gutterBottom variant="h6">
              <b>{t("savingsCalculator:question")}</b>
            </Typography>
            <Typography
              variant="h5"
              color="primary"
              style={{
                fontWeight: "bold",
                textAlign: "center",
                margin: "1rem 0rem",
              }}
            >
              {numeral(monthlyCost).format("$0")}
            </Typography>
            <CustomSlider
              aria-label="Choose monthly utility cost"
              style={{ marginBottom: "1rem" }}
              min={499}
              max={3000}
              onChange={this.updateMonthlyCost}
            />
            <Grid container>
              <Grid style={{ paddingRight: ".5rem" }} item xs={6}>
                <InputLabel id="phase-label">
                  {t("savingsCalculator:connection")}
                </InputLabel>
                <Select
                  style={{ width: "100%" }}
                  value={phase}
                  name="phase"
                  onChange={this.handleSelectChange}
                  labelId="phase-label"
                >
                  <MenuItem value="phase1">
                    {t("savingsCalculator:phase1")}
                  </MenuItem>
                  <MenuItem value="phase2">
                    {t("savingsCalculator:phase2")}
                  </MenuItem>
                  <MenuItem value="phase3">
                    {t("savingsCalculator:phase3")}
                  </MenuItem>
                </Select>
              </Grid>
              <Grid style={{ paddingLeft: ".5rem" }} item xs={6}>
                <InputLabel id="distributor-label">
                  {t("savingsCalculator:distributor")}
                </InputLabel>
                <Select
                  style={{ width: "100%" }}
                  value={distributor}
                  name="distributor"
                  labelId="distributor-label"
                  onChange={this.handleSelectChange}
                >
                  <MenuItem value="cemig">Cemig</MenuItem>
                  <MenuItem value="light">Light</MenuItem>
                </Select>
              </Grid>
            </Grid>
            <Typography gutterBottom style={{ marginTop: "2rem" }} variant="h6">
              <b>{t("savingsCalculator:savings")}</b>
            </Typography>
            <Grid
              style={{ marginBottom: "2rem", marginTop: "2rem" }}
              container
              justifyContent="center"
              spacing={fullScreen ? 1 : 5}
            >
              <Grid item>
                <Typography
                  variant="h4"
                  color="secondary"
                  style={{
                    minWidth: "120px",
                    fontWeight: "bold",
                    textAlign: "center",
                  }}
                >
                  {numeral(this.getSavings() / 12).format("$0")}
                </Typography>
                <Typography
                  style={{ fontWeight: "bold", textAlign: "center" }}
                  variant="body2"
                >
                  {t("savingsCalculator:monthly")}
                </Typography>
              </Grid>
              <Grid
                item
                style={{
                  padding: 0,
                  width: 0,
                  borderRight: "thin solid lightgrey",
                }}
              />
              <Grid item>
                <Typography
                  variant="h4"
                  color="secondary"
                  style={{
                    minWidth: "120px",
                    fontWeight: "bold",
                    textAlign: "center",
                  }}
                >
                  {numeral(this.getSavings()).format("$0")}
                </Typography>
                <Typography
                  style={{ fontWeight: "bold", textAlign: "center" }}
                  variant="body2"
                >
                  {t("savingsCalculator:yearly")}
                </Typography>
              </Grid>
            </Grid>
            <FormHelperText style={{ width: "100%", textAlign: "center" }}>
              *{t("savingsCalculator:disclaimer")}
            </FormHelperText>
            <Grid style={{ textAlign: "center", margin: "2rem" }} xs={12}>
              <Button
                color="secondary"
                style={{
                  borderRadius: "50px",
                  color: "#fff",
                  fontWeight: "bold",
                }}
                size="large"
                variant="contained"
                to="/register"
                component={Link}
              >
                {t("savingsCalculator:save")}
              </Button>
            </Grid>
          </Grid>
        </Grid>
      </Fragment>
    );
  }
}

export default withStyles(styles, { withTheme: true })(
  withMobileDialog()(withTranslator(SavingsCalculator))
);
