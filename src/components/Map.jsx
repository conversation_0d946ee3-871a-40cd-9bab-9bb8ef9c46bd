import React, { Component } from "react";
import { withStyles } from "@material-ui/core/styles";
import classnames from "classnames";
import { Icon, withMobileDialog } from "@material-ui/core";
import ReactMapGL, { Marker, NavigationControl } from "react-map-gl";

import { LocationOn } from "@material-ui/icons";

const defaultHeight = 400;

const styles = (theme) => ({
  locationMarker: {
    fontSize: "2rem",
    overflow: "visible",
    position: "absolute",
    bottom: "-3px", // offset marker to keep centered on zoom
    left: "-18px", // offset marker to keep centered on zoom
  },
  pointer: {
    cursor: "pointer",
  },
  raiseMarker: {
    zIndex: 10,
  },
  roundedCorners: { borderRadius: theme.shape.borderRadius },
  selectedMarker: {
    fontSize: "3.5rem",
    marginLeft: "-.75rem",
  },
});

function getHeight(height) {
  if (!height) return defaultHeight;
  if (typeof height === "function") {
    return height();
  }
  return height;
}

function getFocusPoint(markers, fullScreen, height) {
  let latMax;
  let latMin;
  let longMax;
  let longMin;
  markers.forEach((marker) => {
    const { latitude, longitude } = marker;
    if (latitude === null || longitude === null) return;
    if (latitude >= latMax || typeof latMax === "undefined") latMax = latitude;
    if (latitude <= latMin || typeof latMin === "undefined") latMin = latitude;
    if (longitude >= longMax || typeof longMin === "undefined")
      longMax = longitude;
    if (longitude <= longMin || typeof longMin === "undefined")
      longMin = longitude;
  });
  const longDiff = Math.abs(longMax - longMin);
  const latDiff = Math.abs(longMax - longMin);
  const maxDiff = longDiff > latDiff ? longDiff : latDiff;
  let zoom;
  if (maxDiff > 100) {
    zoom = 1.25;
  } else if (maxDiff > 48) {
    zoom = 1.5;
  } else if (maxDiff > 24) {
    zoom = 2.5;
  } else if (maxDiff > 6) {
    zoom = 3;
  } else if (maxDiff > 2) {
    zoom = 4;
  } else if (maxDiff > 0.75) {
    zoom = 5;
  } else if (maxDiff > 0.2) {
    zoom = 6;
  } else if (maxDiff > 0.15) {
    zoom = 6;
  } else {
    zoom = 6;
  }
  if (fullScreen) {
    zoom *= 0.6;
  }
  if (height && !fullScreen) {
    zoom *= height / defaultHeight;
  }
  zoom = Math.max(zoom, 1);
  return {
    latAvg: (latMax + latMin) / 2,
    longAvg: (longMax + longMin) / 2,
    zoom,
  };
}
class Map extends Component {
  constructor(props) {
    super(props);
    this.state = {
      viewport: {
        longitude: props.longitude || 0,
        latitude: props.latitude || 0,
        width: props.width || defaultHeight,
        height: getHeight(props.height) || defaultHeight,
        zoom: props.zoom || 8,
      },
    };
    // BIND FUNCTIONS
    this.renderMarkers = this.renderMarkers.bind(this);
    this._onViewportChange = this._onViewportChange.bind(this);
    this._resize = this._resize.bind(this);
  }

  static getDerivedStateFromProps(props, state) {
    if (state.viewport.latitude === 0 && state.viewport.longitude === 0) {
      const focusPoint = getFocusPoint(
        props.markers,
        props.fullScreen,
        props.height
      );
      state.viewport.latitude = focusPoint.latAvg;
      state.viewport.longitude = focusPoint.longAvg;
      if (!props.zoom) {
        state.viewport.zoom = focusPoint.zoom;
      }
    }
    return state;
  }

  componentDidMount() {
    window.addEventListener("resize", this._resize);
    this._resize();
  }

  componentWillUnmount() {
    window.removeEventListener("resize", this._resize);
  }

  _resize() {
    const { props } = this;
    let { latitude, longitude } = props;
    if (latitude === 0) {
      latitude = 0.000001;
    }
    if (longitude === 0) {
      longitude = 0.000001;
    }
    this._onViewportChange({
      width: props.width || defaultHeight,
      height: getHeight(props.height) || defaultHeight,
      latitude: props.latitude || 0,
      longitude: props.longitude || 0,
      zoom: props.zoom || 8,
    });
  }

  _onViewportChange(viewport) {
    if (viewport.zoom < 1 || viewport.zoom > 20) return;
    this.setState({ viewport });
  }

  renderMarkers() {
    const { classes, markers } = this.props;
    const jsx = [];
    if (!markers) return null;
    markers.forEach((marker, index) => {
      if (!marker.latitude || !marker.longitude) return;
      jsx.push(
        <Marker
          key={`marker-${index}`} // eslint-disable-line
          longitude={marker.longitude}
          latitude={marker.latitude}
          className={
            marker.isSelected && marker.isSelected()
              ? classes.raiseMarker
              : null
          }
        >
          {marker.customIconClassNames ? (
            <Icon
              onMouseOver={marker.onMarkerHover || null}
              onFocus={marker.onMarkerHover || null}
              onMouseOut={marker.onMarkerHoverExit || null}
              onBlur={marker.onMarkerHoverExit || null}
              onClick={marker.onMarkerClick || null}
              color="primary"
              style={{
                color: marker.color ? marker.color : null,
              }}
              className={classnames(
                classes.locationMarker,
                marker.customIconClassNames,
                marker.isSelected && marker.isSelected()
                  ? classes.selectedMarker
                  : null,
                marker.onMarkerClick ? classes.pointer : null
              )}
            />
          ) : (
            <LocationOn
              onMouseOver={marker.onMarkerHover || null}
              onFocus={marker.onMarkerHover || null}
              onMouseOut={marker.onMarkerHoverExit || null}
              onBlur={marker.onMarkerHoverExit || null}
              onClick={marker.onMarkerClick || null}
              color="primary"
              style={{
                color: marker.color ? marker.color : null,
              }}
              className={classnames(
                classes.locationMarker,
                marker.customIconClassNames,
                marker.isSelected && marker.isSelected()
                  ? classes.selectedMarker
                  : null,
                marker.onMarkerClick ? classes.pointer : null
              )}
            />
          )}
        </Marker>
      );
    });
    return jsx;
  }

  render() {
    const {
      alwaysShowZoom,
      classes,
      disableScroll,
      fullScreen,
      markers,
      roundedCorners,
    } = this.props;
    const { viewport } = this.state;
    if (!markers) {
      return null;
    }
    // Change the condition to check for null or undefined
    if (
      viewport.longitude === null ||
      viewport.longitude === undefined ||
      viewport.latitude === null ||
      viewport.latitude === undefined
    ) {
      return null;
    }
    const markerComponent = markers ? this.renderMarkers() : null;
    return (
      <ReactMapGL
        className={roundedCorners ? classes.roundedCorners : null}
        scrollZoom={!disableScroll}
        // dragPan={false}
        mapboxApiAccessToken="pk.eyJ1IjoiZ3JlaW5oYXJkIiwiYSI6ImNsM2t1NmZlYTAxbmYzY281NTdoZWlxZGIifQ.h8FxGok6bzEuX_3p2EtKiQ"
        {...viewport}
        onViewportChange={(oViewport) => this._onViewportChange(oViewport)}
      >
        {fullScreen && !alwaysShowZoom ? null : (
          <NavigationControl
            showCompass={false}
            style={{ right: 10, top: 10 }}
          />
        )}
        {markerComponent}
      </ReactMapGL>
    );
  }
}
export default withMobileDialog()(withStyles(styles)(Map));
