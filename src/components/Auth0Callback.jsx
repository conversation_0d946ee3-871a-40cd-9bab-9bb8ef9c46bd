import React from "react";
import { useAuth0 } from "@auth0/auth0-react";
import { Grid, CircularProgress, Typography } from "@material-ui/core";

const Auth0Callback = () => {
  const { isLoading, error, isAuthenticated } = useAuth0();

  console.log(
    "Auth0Callback - isLoading:",
    isLoading,
    "error:",
    error,
    "isAuthenticated:",
    isAuthenticated
  );

  if (error) {
    return (
      <Grid
        container
        justifyContent="center"
        alignItems="center"
        style={{ height: "100vh", background: "#fff" }}
      >
        <Grid item style={{ textAlign: "center" }}>
          <Typography variant="h6" color="error" gutterBottom>
            Authentication Error
          </Typography>
          <Typography variant="body1">
            {error.message || "An error occurred during authentication"}
          </Typography>
        </Grid>
      </Grid>
    );
  }

  if (isLoading) {
    return (
      <Grid
        container
        justifyContent="center"
        alignItems="center"
        style={{ height: "100vh", background: "#fff" }}
      >
        <Grid item style={{ textAlign: "center" }}>
          <CircularProgress size={60} />
          <Typography variant="h6" style={{ marginTop: 16 }}>
            Completing authentication...
          </Typography>
        </Grid>
      </Grid>
    );
  }

  // This should not render as the redirect callback should handle navigation
  return null;
};

export default Auth0Callback;
