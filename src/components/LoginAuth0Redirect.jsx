import React from "react";
import { Navigate } from "react-router-dom";
import { useAuth0 } from "@auth0/auth0-react";
import { withStyles } from "@material-ui/core/styles";
import { Grid, Typography, Button, useTheme } from "@material-ui/core";
import cloudinary from "cloudinary-core";

import withSnackbar from "./hocs/withSnackbar";
import withTranslator from "./hocs/withTranslator";

const cl = new cloudinary.Cloudinary({
  cloud_name: process.env.REACT_APP_CLOUDINARY_CLOUD_NAME,
  secure: true,
});

const styles = (theme) => ({
  container: {
    background: "#fff",
    height: "100vh",
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
  },
  content: {
    textAlign: "center",
    maxWidth: "400px",
    padding: theme.spacing(4),
  },
  logo: {
    width: "4rem",
    height: "4rem",
    marginBottom: theme.spacing(2),
  },
  title: {
    fontWeight: "bold",
    marginBottom: theme.spacing(1),
  },
  subtitle: {
    marginBottom: theme.spacing(3),
  },
  loginButton: {
    marginTop: theme.spacing(2),
    padding: "12px 48px",
    fontSize: "1.1rem",
  },
});

const Login = (props) => {
  const { isAuthenticated, isLoading, loginWithRedirect } = useAuth0();
  const theme = useTheme();
  const { classes, i18n } = props;

  const handleLogin = () => {
    loginWithRedirect({
      authorizationParams: {
        screen_hint: "login",
        scope: "openid profile email",
        ui_locales: "pt-BR",
      },
      appState: {
        returnTo: "/dashboard?welcome=1",
      },
    });
  };

  if (isLoading) return null;
  if (isAuthenticated) return <Navigate to="/dashboard" />;

  return (
    <div className={classes.container}>
      <div className={classes.content}>
        <img
          className={classes.logo}
          src={cl.url("energea/energea-global-favicon", {
            width: "80",
            quality: "auto",
            format: "WebP",
          })}
          alt="Energea logo"
        />
        <Typography
          variant="h4"
          className={classes.title}
          style={{ color: theme.palette.primary.main }}
        >
          {i18n.t("login", "Login")}
        </Typography>
        <Typography
          variant="body1"
          className={classes.subtitle}
          style={{ color: theme.palette.text.secondary }}
        >
          {i18n.t("loginSubtitle", "Access your Energea Brasil dashboard")}
        </Typography>

        <Button
          variant="contained"
          color="primary"
          size="large"
          onClick={handleLogin}
          className={classes.loginButton}
        >
          {i18n.t("loginButton", "Login with Auth0")}
        </Button>

        <Typography
          variant="caption"
          style={{
            display: "block",
            marginTop: theme.spacing(2),
            color: theme.palette.text.secondary,
          }}
        >
          {i18n.t(
            "loginNote",
            "You will be redirected to Auth0 for secure authentication"
          )}
        </Typography>
      </div>
    </div>
  );
};

export default withSnackbar(
  withStyles(styles, { withTheme: true })(withTranslator(Login))
);
