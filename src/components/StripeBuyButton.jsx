import React, { useEffect } from "react";

const StripeBuyButton = ({ publishableKey, buyButtonId }) => {
  useEffect(() => {
    // Function to dynamically add the Stripe script to the page
    const loadStripeScript = () => {
      if (
        document.querySelector(
          'script[src="https://js.stripe.com/v3/buy-button.js"]'
        )
      ) {
        // Script already added
        return;
      }
      const script = document.createElement("script");
      script.src = "https://js.stripe.com/v3/buy-button.js";
      script.async = true;
      document.body.appendChild(script);
    };

    loadStripeScript();
  }, []); // Empty array means this effect runs once on mount

  return (
    <stripe-buy-button
      buy-button-id={buyButtonId}
      publishable-key={publishableKey}
    ></stripe-buy-button>
  );
};

export default StripeBuyButton;
