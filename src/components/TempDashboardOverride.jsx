import React, { useState, useEffect } from "react";
import { graphql } from "react-apollo";
import { useOktaAuth } from "@okta/okta-react";
import { Alert, AlertTitle } from "@material-ui/lab";

import {
  CircularProgress,
  Grid,
  useTheme,
  withStyles,
  withMobileDialog,
} from "@material-ui/core";

import { loader } from "graphql.macro";

import withTranslator from "./hocs/withTranslator";

const dashboardQuery = loader("../queries/Dashboard.graphql");

const styles = (theme) => ({
  sections: {
    padding: theme.spacing(2),
    borderTop: "5px solid #fff",
  },
  sectionTitleDividers: {
    width: "100%",
    margin: "1rem 0",
  },
});
const Dashboard = (props) => {
  const { oktaAuth, authState } = useOktaAuth();
  const theme = useTheme(); // Access the theme object here
  const [selectedConsumerUnit, setSelectedConsumerUnit] = useState(null);
  const [selectedCustomer, setSelectedCustomer] = useState(null);
  const { data } = props;
  let curConsumerUnitData;
  const brContact = data?.me?.brContact;
  // if (!brContact) {
  //   return null;
  // }
  if (selectedConsumerUnit) {
    brContact.brContactsBrCustomers.forEach((customer) => {
      if (selectedCustomer === customer.brCustomer.id) {
        customer.brCustomer.brConsumerUnits.forEach((consumerUnit) => {
          if (selectedConsumerUnit === consumerUnit.id) {
            curConsumerUnitData = consumerUnit;
          }
        });
      }
    });
  }
  // console.log("HIT IT", data);
  useEffect(() => {
    if (authState?.isPending) {
      return;
    }
    if (authState && !authState?.isPending && !authState?.isAuthenticated) {
      oktaAuth.signOut({
        postLogoutRedirectUri: `${window.location.origin}/login`,
      });
    }
    if (authState?.isAuthenticated && !data.loading && !data?.me?.brContact) {
      oktaAuth.signOut({
        postLogoutRedirectUri: `${window.location.origin}/login`,
      });
    }
  }, [authState]); // Dependencies for useEffect
  if (data.loading) {
    return (
      <Grid
        container
        justifyContent="center"
        alignItems="center"
        style={{ minHeight: "100vh" }}
      >
        <CircularProgress />
      </Grid>
    );
  }
  return (
    <>
      <Grid
        container
        justifyContent="center"
        // alignItems="center"
        style={{ background: theme.palette.primary.main, padding: "2rem 1rem" }}
      >
        <Grid item xs={12} md={10} lg={8}>
          <Grid container spacing={5} direction="column">
            <Grid item component={Alert} style={{ marginTop: "2rem" }}>
              <AlertTitle>Bem-vindo à Energea!</AlertTitle>
              Obrigado por criar uma conta! Estamos revisando suas informações e
              entraremos em contato em breve com os próximos passos. Você está a
              caminho de economizar dinheiro na sua conta de luz!
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </>
  );
};

export default withStyles(styles, { withTheme: true })(
  graphql(dashboardQuery)(withMobileDialog()(withTranslator(Dashboard)))
);
