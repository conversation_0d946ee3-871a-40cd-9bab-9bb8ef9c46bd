import React, { useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { Auth0Provider, useAuth0 } from "@auth0/auth0-react";

// Component to handle Auth0 token storage
const Auth0TokenHandler = ({ children }) => {
  const {
    getIdTokenClaims,
    isAuthenticated,
    isLoading,
    getAccessTokenSilently,
  } = useAuth0();
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    // Check for existing tokens in localStorage
    const existingToken = localStorage.getItem("id_token");
    const tokenExpiry = localStorage.getItem("id_token_expires_at");
    const isTokenValid =
      existingToken && tokenExpiry && Date.now() < parseInt(tokenExpiry) * 1000;

    const storeToken = async () => {
      if (isAuthenticated && !isLoading) {
        try {
          const tokenClaims = await getIdTokenClaims();
          if (tokenClaims && tokenClaims.__raw) {
            localStorage.setItem("id_token", tokenClaims.__raw);
            // Store expiration time
            if (tokenClaims.exp) {
              localStorage.setItem(
                "id_token_expires_at",
                tokenClaims.exp.toString()
              );
            }
          }
        } catch (error) {
          console.error("Error storing Auth0 token:", error);
        }
      } else if (!isAuthenticated && !isLoading && !isTokenValid) {
        // Check if this might be a manual authentication scenario
        const token = localStorage.getItem("id_token");
        const tokenExpiry = localStorage.getItem("id_token_expires_at");

        if (token && tokenExpiry) {
          // Double-check token validity before clearing
          const isStillValid = Date.now() < parseInt(tokenExpiry) * 1000;
          if (isStillValid) {
            return; // Don't clear valid tokens
          }
        }
        // Clear tokens when not authenticated and no valid token exists
        localStorage.removeItem("id_token");
        localStorage.removeItem("id_token_expires_at");
      } else if (!isAuthenticated && !isLoading && isTokenValid) {
        // Check if this is a manual authentication (from registration)
        // In this case, we don't want to try to refresh Auth0 state since we don't have a refresh token
        const isManualAuth = sessionStorage.getItem("manual_auth_success");

        if (!isManualAuth) {
          // Only try to refresh Auth0 state if this is not manual authentication
          try {
            getAccessTokenSilently().catch((error) => {
              console.error("Failed to refresh Auth0 state:", error);
            });
          } catch (error) {
            console.error("Error attempting to refresh Auth0 state:", error);
          }
        }
      }
    };

    storeToken();
  }, [isAuthenticated, isLoading, getIdTokenClaims, getAccessTokenSilently]);

  // Fallback redirect to dashboard - only for immediate post-callback scenarios
  // This should only trigger if the primary onRedirectCallback fails
  useEffect(() => {
    // Skip fallback redirect if this is manual authentication
    const isManualAuth = sessionStorage.getItem("manual_auth_success");
    if (isManualAuth) {
      return;
    }

    // Only redirect if user just became authenticated and we have the login flag
    if (isAuthenticated && !isLoading && location.pathname === "/") {
      const hasRecentLogin = sessionStorage.getItem("auth0_login_redirect");

      if (hasRecentLogin) {
        // Clear the flag immediately to prevent multiple redirects
        sessionStorage.removeItem("auth0_login_redirect");

        // Only redirect if this appears to be immediately after callback
        // Use a very short window to catch callback failures
        const now = Date.now();
        const loginTime = parseInt(
          sessionStorage.getItem("auth0_login_time") || "0"
        );
        const timeSinceLogin = now - loginTime;

        // Only redirect if login was very recent (within 5 seconds) and we're on home page
        // AND we haven't been to the dashboard yet (to avoid redirect loops)
        if (
          timeSinceLogin < 5000 &&
          timeSinceLogin > 0 &&
          !sessionStorage.getItem("dashboard_visited")
        ) {
          // Set flag to prevent future fallback redirects
          sessionStorage.setItem("dashboard_visited", "true");
          setTimeout(() => {
            navigate("/dashboard?welcome=1", { replace: true });
          }, 100);
        }

        // Clean up the login time as well
        sessionStorage.removeItem("auth0_login_time");
      }
    }
  }, [isAuthenticated, isLoading, location.pathname, navigate]);

  return children;
};

const Auth0ProviderWithHistory = ({ children }) => {
  const navigate = useNavigate();

  const onRedirectCallback = (appState) => {
    // Clear the login redirect flag since primary redirect is working
    sessionStorage.removeItem("auth0_login_redirect");
    sessionStorage.removeItem("auth0_login_time");

    // Set flag to indicate we've successfully redirected to dashboard
    sessionStorage.setItem("dashboard_visited", "true");

    const returnTo = appState?.returnTo || "/dashboard?welcome=1";

    // Use setTimeout to ensure Auth0 has finished processing
    setTimeout(() => {
      navigate(returnTo, { replace: true });
      console.log("Navigation completed to:", returnTo);
    }, 100);
  };

  // Check if Auth0 environment variables are set
  const domain = process.env.REACT_APP_AUTH0_DOMAIN;
  const clientId = process.env.REACT_APP_AUTH0_CLIENT_ID;

  if (!domain || !clientId) {
    console.error("Auth0 environment variables not set:", {
      REACT_APP_AUTH0_DOMAIN: domain,
      REACT_APP_AUTH0_CLIENT_ID: clientId,
    });

    // Return children without Auth0Provider if env vars are missing
    // This allows the app to still render and show the error message
    return children;
  }

  return (
    <Auth0Provider
      domain={domain}
      clientId={clientId}
      authorizationParams={{
        redirect_uri: `${window.location.origin}/auth/callback`,
        scope: "openid profile email",
      }}
      onRedirectCallback={onRedirectCallback}
      useRefreshTokens
      cacheLocation="localstorage"
    >
      <Auth0TokenHandler>{children}</Auth0TokenHandler>
    </Auth0Provider>
  );
};

export default Auth0ProviderWithHistory;
