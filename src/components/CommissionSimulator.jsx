import React, { useState } from "react";
import {
  Grid,
  TextField,
  Typography,
  withStyles,
  useTheme,
} from "@material-ui/core";
import moment from "moment";

import withTranslator from "./hocs/withTranslator";
import { Line } from "react-chartjs-2";
import numeral from "numeral";
import { getGradient } from "../global";

const styles = (theme) => ({});

const CommissionSimulator = (props) => {
  const {
    currentActiveConsumerUnitCount,
    currentActiveConsumerUnitAverageConsumption,
  } = props;
  const theme = useTheme();
  const [newCustomersPerMonth, setNewCustomerPerMonth] = useState(
    currentActiveConsumerUnitCount > 0 ? 0 : 5
  );
  const [averageCommissionRate, setAverageCommissionRate] = useState(0.05);
  const [numberOfYearsModeled, setNumberOfYearsModeled] = useState(20);

  const kWhPurchasedFromUtilityFirst = 100;
  const electricityCost = 1.07;
  const averageDiscountRate = 0.25;
  const residualCommissionPerMonthPerCustomer =
    Math.max(
      currentActiveConsumerUnitAverageConsumption -
        kWhPurchasedFromUtilityFirst,
      0
    ) *
    (1 - averageDiscountRate) *
    electricityCost *
    averageCommissionRate;

  const currentCustomersChartData = [];
  const additionalCustomersChartData = [];
  if (
    (newCustomersPerMonth || newCustomersPerMonth === 0) &&
    (averageCommissionRate || averageCommissionRate === 0)
  ) {
    const currentMonth = moment().add(1, "month").startOf("month");
    const endMonth = moment()
      .add(numberOfYearsModeled, "years")
      .startOf("month");
    let numberOfAdditionalCustomers = newCustomersPerMonth;
    let totalEarnedFromCurrentCustomers =
      (currentActiveConsumerUnitCount || 0) *
      residualCommissionPerMonthPerCustomer;
    let totalEarnedFromAdditionalCustomers =
      numberOfAdditionalCustomers * residualCommissionPerMonthPerCustomer;
    while (currentMonth.isSameOrBefore(endMonth)) {
      currentCustomersChartData.push({
        x: currentMonth.format("MMM D, YYYY"),
        y: totalEarnedFromCurrentCustomers,
      });
      additionalCustomersChartData.push({
        x: currentMonth.format("MMM D, YYYY"),
        y: totalEarnedFromAdditionalCustomers,
      });
      numberOfAdditionalCustomers += newCustomersPerMonth;
      totalEarnedFromCurrentCustomers +=
        (currentActiveConsumerUnitCount || 0) *
        residualCommissionPerMonthPerCustomer;
      totalEarnedFromAdditionalCustomers +=
        numberOfAdditionalCustomers * residualCommissionPerMonthPerCustomer;
      currentMonth.add(1, "month");
    }
  }
  const totalCommission =
    (currentCustomersChartData.length > 0
      ? currentCustomersChartData[currentCustomersChartData.length - 1].y
      : 0) +
    (additionalCustomersChartData.length > 0
      ? additionalCustomersChartData[additionalCustomersChartData.length - 1].y
      : 0);
  return (
    <Grid container item xs={12} direction="column">
      <Grid item container justifyContent="space-between" alignItems="center">
        <Grid item>
          <Typography variant="body1">Simular comissão</Typography>
        </Grid>
        <Grid item>
          <Grid
            container
            direction="column"
            style={{ color: theme.palette.secondary.main }}
            alignItems="flex-end"
          >
            <Grid item>
              <Typography variant="h1">
                R${numeral(totalCommission).format("0[.]0a")}
              </Typography>
            </Grid>
            <Grid item>
              <Typography variant="body2">
                <b>comissão estimada</b>
              </Typography>
            </Grid>
          </Grid>
        </Grid>
      </Grid>
      <Grid item>
        <Grid container spacing={2}>
          <Grid item container spacing={2}>
            <Grid item>
              <TextField
                required
                style={{ background: theme.palette.white.main }}
                variant="outlined"
                label="Novos clientes por mês"
                value={newCustomersPerMonth}
                type="number"
                onChange={(event) => {
                  let val = parseFloat(event.target.value);
                  if (isNaN(val)) val = "";
                  if (val < 0 && val !== "") val = 0;
                  setNewCustomerPerMonth(val);
                }}
                fullWidth
              />
            </Grid>
            {/* <Grid item>
              <TextField
                required
                style={{ background: theme.palette.white.main }}
                variant="outlined"
                label="Taxa média de comissão"
                value={averageCommissionRate}
                type="number"
                onChange={(event) => {
                  let val = parseFloat(event.target.value);
                  if (isNaN(val)) val = "";
                  if (val > 1) val = 1;
                  if (val < 0 && val !== "") val = 0;
                  setAverageCommissionRate(val);
                }}
                fullWidth
              />
            </Grid> */}
            <Grid item>
              <TextField
                required
                style={{ background: theme.palette.white.main }}
                variant="outlined"
                label="Nº de anos simulados"
                value={numberOfYearsModeled}
                type="number"
                onChange={(event) => {
                  let val = parseInt(event.target.value, 10);
                  if (isNaN(val)) val = "";
                  if (val > 20) val = 20;
                  if (val < 1 && val !== "") val = 1;
                  setNumberOfYearsModeled(val);
                }}
                fullWidth
              />
            </Grid>
          </Grid>
          <Grid item xs={12}>
            <Line
              height={300}
              data={{
                // labels: chartData.map((pt) => pt.date),
                datasets: [
                  {
                    label: "Comissão de clientes atuais",
                    data: currentCustomersChartData,
                    borderWidth: 4,
                    borderColor: theme.palette.secondary.main,
                    backgroundColor: (context) =>
                      getGradient(context, theme.palette.secondary.main),
                    fill: true,
                    pointRadius: 0,
                  },
                  {
                    label: "Comissão de clientes adicionais",
                    data: additionalCustomersChartData,
                    borderWidth: 2,
                    borderDash: [5, 5], // [dash length, gap length]
                    borderColor: theme.palette.secondary.main,
                    // backgroundColor: (context) =>
                    //   getGradient(context, theme.palette.secondary.main),
                    fill: false,
                    pointRadius: 0,
                  },
                ],
              }}
              options={{
                maintainAspectRatio: false,
                plugins: {
                  legend: {
                    display: false,
                  },
                  tooltip: {
                    mode: "index",
                    intersect: false,
                    position: "nearest",
                    callbacks: {
                      title: (tooltipItems) => {
                        const tooltipItem = tooltipItems?.[0];
                        if (!tooltipItem) return null;
                        return new Date(
                          tooltipItem.parsed.x
                        ).toLocaleDateString("pt-br", {
                          month: "short",
                          year: "numeric",
                        });
                      },
                      label: (tooltipItem) =>
                        `${tooltipItem.dataset.label}: ${numeral(
                          tooltipItem.raw.y
                        ).format("$0,0")}`,
                    },
                  },
                },
                scales: {
                  x: {
                    type: "time",
                    time: {
                      unit: "month",
                    },
                    ticks: {
                      callback: (value) =>
                        new Date(value).toLocaleDateString("pt-br", {
                          month: "short",
                          year: "numeric",
                        }),
                    },
                  },
                  y: {
                    stacked: true,
                    beginAtZero: true,
                    title: {
                      text: "Comissão simulada recebida (R$)",
                      display: true,
                    },
                    ticks: {
                      callback: (value) => numeral(value).format("0,0[.]00"),
                    },
                  },
                },
              }}
            />
          </Grid>
          <Grid item xs={12}>
            <Typography variant="caption">
              <b>Aviso</b>: O gráfico acima é uma simulação do total de
              comissões potenciais recebidas ao longo dos próximos{" "}
              {numberOfYearsModeled || 0} anos. Esta simulação é apenas para
              fins ilustrativos e não constitui previsão ou garantia de
              resultados reais. Baseia-se nas seguintes premissas:
              <l>
                <li>
                  Registro de {newCustomersPerMonth} novas unidades consumidoras
                  por mês
                </li>
                <li>
                  Consumo médio mensal de{" "}
                  {numeral(currentActiveConsumerUnitAverageConsumption).format(
                    "0,0[.]0"
                  )}{" "}
                  kWh por unidade
                </li>
                <li>
                  Preço da eletricidade de R${" "}
                  {numeral(electricityCost).format("0[.]00")}/kWh
                </li>
                <li>
                  Taxa média de desconto ao cliente de{" "}
                  {numeral(averageDiscountRate).format("%")}
                </li>
                <li>
                  Taxa média de comissão de{" "}
                  {numeral(averageCommissionRate).format("%")}
                </li>
                <li>0% de inadimplência dos clientes</li>
                <li>Não inclui nenhum cliente atualmente ativo</li>
              </l>
            </Typography>
          </Grid>
        </Grid>
      </Grid>
    </Grid>
  );
};

export default withStyles(styles, { withTheme: true })(
  withTranslator(CommissionSimulator)
);
