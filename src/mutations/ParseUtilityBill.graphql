mutation ParseUtilityBill($input: ParseUtilityBillInput!) {
  parseUtilityBill(input: $input) {
    installationCode
    utilityCustomerCode
    address1
    address2
    city
    state
    district
    postalCode
    janConsumption
    febConsumption
    marConsumption
    aprConsumption
    mayConsumption
    junConsumption
    julConsumption
    augConsumption
    sepConsumption
    octConsumption
    novConsumption
    decConsumption
  }
}
