import React from "react";
import { Route } from "react-router";

export default (
  <React.Fragment>
    <Route exact path="/" />
    <Route exact path="/home" />
    <Route exact path="/how-it-works" />
    <Route exact path="/savings-calculator" />
    <Route exact path="/benefits" />
    <Route exact path="/register" />
    {/* <Route
	  exact
	  path="/learn-more"
	  component={withPageMargin(withTranslator(LearnMore))}
	/> */}
    <Route exact path="/who-we-are" />
    <Route exact path="/clients" />
    <Route exact path="/projects" />
    <Route exact path="/faq" />
    {/* <Route
	  exact
	  path="/home"
	/> */}
    <Route exact path="/contact" />
    <Route exact path="/parceiros" />
    <Route exact path="/cadastro-parceiro" />

    <Route exact path="/*" />
  </React.Fragment>
);
