# Sequel Sans Fonts Directory

This directory contains the Sequel Sans font files for the Energea BR application.

## Font Format:
Using TTF (TrueType) format for optimal web performance and browser compatibility.

## Required Sequel Sans Files:
```
src/assets/fonts/
├── Sequel-Sans-Roman-Head.ttf       (Headers/Titles - 400 weight)
├── Sequel-Sans-Roman-Disp.ttf       (Body text - 400 weight)
├── Sequel-Sans-Medium-Disp.ttf      (Medium weight - 500)
├── Sequel-Sans-Semi-Bold-Disp.ttf   (Semi-bold - 600)
├── Sequel-Sans-Heavy-Head.ttf       (Heavy weight - 800)
└── fonts.css
```

## Font Weight Mapping:
- **Roman Head**: 400 (normal) - Used for headers and titles
- **Roman Display**: 400 (normal) - Used for body text
- **Medium Display**: 500 (medium) - Used for emphasized text
- **Semi Bold Display**: 600 (semi-bold) - Used for bold text
- **Heavy Head**: 800 (heavy) - Used for special emphasis/hero text

## Manual Setup Required:
⚠️ **IMPORTANT**: You need to manually copy the font files from the energea-app codebase:

1. Copy the following files from `energea-app/client/assets/font/` to `src/assets/fonts/`:
   - Sequel-Sans-Roman-Head.ttf
   - Sequel-Sans-Roman-Disp.ttf
   - Sequel-Sans-Medium-Disp.ttf
   - Sequel-Sans-Semi-Bold-Disp.ttf
   - Sequel-Sans-Heavy-Head.ttf

## Setup Complete:
✅ Font CSS definitions created in fonts.css
✅ Imported in src/index.css
✅ Theme.js updated to use Sequel Sans
✅ sfform.css updated to use Sequel Sans
⚠️ Font files need to be manually copied (see above)

## Migration Summary:
- Replaced all Lato font references with Sequel Sans
- Replaced all Montserrat font references with Sequel Sans
- Updated Material-UI theme configuration
- Updated global CSS imports
- Maintained fallback fonts for better compatibility
